@echo off
REM ###############################################################
REM # Batch file to run the Advanced Python File Manager.         #
REM # This version uses %~dp0 to find the script in its own dir.  #
REM ###############################################################

echo Launching Advanced Python File Manager...

REM %~dp0 expands to the drive and path of the batch file.
REM This ensures Python can find the script no matter where it's run from.
py -3 "%~dp0Advanced_Python_File_Manager.py"