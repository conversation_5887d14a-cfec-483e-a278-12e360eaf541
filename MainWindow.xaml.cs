﻿using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Imaging;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Shapes;
using System.Windows.Media;
using Microsoft.Win32;
using CaptureMasterPro.Services;
using System.Text;
using System.Collections.Generic;
using System.Runtime.InteropServices;
// Importazione esplicita per risolvere l'ambiguità con Path
using Path = System.Windows.Shapes.Path;

namespace CaptureMasterPro
{
    // Class to represent a window item in the list
    public class WindowItem
    {
        public string Title { get; set; } = string.Empty;
        public IntPtr Handle { get; set; }
        
        public override string ToString()
        {
            return Title;
        }
    }
    
    public partial class MainWindow : Window
    {
        // Area selection variables
        private Point startPoint;
        private Rectangle selectionRectangle = null!;
        private Canvas selectionCanvas = null!;
        private bool isSelecting = false;
        private readonly ScreenCaptureService _captureService;
        private readonly AutoScrollService _scrollService;
        private readonly ImageEditingService _imageService;
        private BitmapSource? _currentImage;
        private ScrollCaptureOverlayWindow? _scrollCaptureOverlay; // Added for new scroll capture
        private double _currentZoom = 1.0;
        private const double ZOOM_FACTOR = 0.1; // Zoom 10% each step


        private bool _isTestMode = false;
        private IntPtr _testWindowHandle = IntPtr.Zero;


        // Method to get application icon from window handle
        [DllImport("user32.dll")]
        private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);
        
        private System.Drawing.Icon? GetAppIcon(IntPtr hwnd)
        {
            try
            {
                // Get process ID from window handle
                GetWindowThreadProcessId(hwnd, out uint processId);
                if (processId == 0) return null;
                
                // Get process
                var process = System.Diagnostics.Process.GetProcessById((int)processId);
                if (process == null) return null;
                
                // Get icon from main module
                if (!string.IsNullOrEmpty(process.MainModule?.FileName))
                {
                    return System.Drawing.Icon.ExtractAssociatedIcon(process.MainModule.FileName);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting icon: {ex.Message}");
            }
            
            return null;
        }

        public MainWindow()
        {
            InitializeComponent();
            
            _captureService = new ScreenCaptureService();
            _scrollService = new AutoScrollService();
            _imageService = new ImageEditingService();
            
            // Hide the floating scroll control and image display initially
            floatingScrollControl.Visibility = Visibility.Collapsed;
            imageDisplayGrid.Visibility = Visibility.Collapsed;
            
            // Start with just the toolbar visible
            SizeToContent = SizeToContent.WidthAndHeight;
            
            UpdateStatus("Ready");
            // Initialize zoom
            UpdateZoom(0);
        }

        public void SetCurrentImage(BitmapSource image)
        {
            _currentImage = image;
            if (imgScreenshot != null) 
            {
                imgScreenshot.Source = _currentImage;
            }
        }

        public void ShowImageDisplay()
        {
            if (imageDisplayGrid != null && imageRowDefinition != null)
            {
                imageDisplayGrid.Visibility = Visibility.Visible;
                imageRowDefinition.Height = new GridLength(1, GridUnitType.Star);
            }
        }
        
        private void btnScrollUp_Click(object sender, RoutedEventArgs e)
        {
            if (_isTestMode && _testWindowHandle != IntPtr.Zero)
            {
                SetForegroundWindow(_testWindowHandle);
                _scrollService.PageUp(_testWindowHandle);
                UpdateStatus("Test Scroll Up sent.");
            }
        }
        
        private void btnScrollDown_Click(object sender, RoutedEventArgs e)
        {
            if (_isTestMode && _testWindowHandle != IntPtr.Zero)
            {
                SetForegroundWindow(_testWindowHandle);
                _scrollService.PageDown(_testWindowHandle);
                UpdateStatus("Test Scroll Down sent.");
            }
        }
        private void btnCaptureArea_Click(object sender, RoutedEventArgs e)
        {
            // Hide the application window temporarily
            this.Hide();
            System.Threading.Thread.Sleep(200); // Short delay to ensure window is hidden
            
            // Create a transparent window for area selection
            Window selectionWindow = new Window
            {
                WindowStyle = WindowStyle.None,
                ResizeMode = ResizeMode.NoResize,
                AllowsTransparency = true,
                Background = new SolidColorBrush(Color.FromArgb(1, 0, 0, 0)),
                WindowState = WindowState.Maximized,
                Topmost = true,
                Cursor = Cursors.Cross // Change cursor to crosshair for selection
            };
            
            // Create a canvas for drawing the selection rectangle
            selectionCanvas = new Canvas();
            selectionWindow.Content = selectionCanvas;
            
            // Add mouse event handlers
            selectionWindow.MouseLeftButtonDown += SelectionWindow_MouseLeftButtonDown;
            selectionWindow.MouseMove += SelectionWindow_MouseMove;
            selectionWindow.MouseLeftButtonUp += SelectionWindow_MouseLeftButtonUp;
            
            // Show the selection window
            selectionWindow.Show();
        }
        
        private void SelectionWindow_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            isSelecting = true;
            startPoint = e.GetPosition(selectionCanvas);
            
            // Create a new selection rectangle
            selectionRectangle = new Rectangle
            {
                Stroke = new SolidColorBrush(Colors.Red),
                StrokeThickness = 2,
                Fill = new SolidColorBrush(Color.FromArgb(50, 173, 216, 230))
            };
            
            Canvas.SetLeft(selectionRectangle, startPoint.X);
            Canvas.SetTop(selectionRectangle, startPoint.Y);
            selectionCanvas.Children.Add(selectionRectangle);
        }
        
        private void SelectionWindow_MouseMove(object sender, MouseEventArgs e)
        {
            if (!isSelecting) return;
            
            var currentPoint = e.GetPosition(selectionCanvas);
            
            // Update rectangle dimensions
            double width = Math.Abs(currentPoint.X - startPoint.X);
            double height = Math.Abs(currentPoint.Y - startPoint.Y);
            
            // Update rectangle position (in case of dragging from right to left or bottom to top)
            double left = Math.Min(startPoint.X, currentPoint.X);
            double top = Math.Min(startPoint.Y, currentPoint.Y);
            
            Canvas.SetLeft(selectionRectangle, left);
            Canvas.SetTop(selectionRectangle, top);
            selectionRectangle.Width = width;
            selectionRectangle.Height = height;
        }
        
        private void SelectionWindow_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (!isSelecting) return;
            
            isSelecting = false;
            var currentPoint = e.GetPosition(selectionCanvas);
            
            // Calculate the selection area
            double left = Math.Min(startPoint.X, currentPoint.X);
            double top = Math.Min(startPoint.Y, currentPoint.Y);
            double width = Math.Abs(currentPoint.X - startPoint.X);
            double height = Math.Abs(currentPoint.Y - startPoint.Y);
            
            Console.WriteLine($"Area selection: Left={left}, Top={top}, Width={width}, Height={height}");
            
            try
            {
                // Validate selection size
                if (width < 5 || height < 5)
                {
                    Console.WriteLine("Selection area too small, aborting capture");
                    MessageBox.Show("The selected area is too small. Please select a larger area.", "Small Selection", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // Close the selection window
                    Window selectionWindow = (Window)sender;
                    selectionWindow.Close();
                    
                    // Show the main window again
                    this.Show();
                    this.Activate();
                    return;
                }
                
                // Close the selection window
                var currentSelectionWindow = (Window)sender;
                currentSelectionWindow.Close();
                
                // Capture the selected area - use exact screen coordinates
                System.Drawing.Rectangle bounds = new System.Drawing.Rectangle(
                    (int)left, (int)top, (int)width, (int)height);
                
                // Add a small delay to ensure accurate capture
                System.Threading.Thread.Sleep(200);
                
                Console.WriteLine($"Attempting to capture area: {bounds}");
                
                // Capture the screenshot of the selected area with exact bounds
                _currentImage = _captureService.CaptureArea(bounds);
                Console.WriteLine($"Captured image: {(_currentImage != null ? $"{_currentImage.PixelWidth}x{_currentImage.PixelHeight}" : "null")}");
                
                if (_currentImage == null)
                {
                    throw new Exception("Failed to capture image");
                }
                
                // Show the main window again
                this.Show();
                this.Activate();
                
                // Set the image source
                imgScreenshot.Source = _currentImage;
                
                // Make sure the image display area is visible
                imageDisplayGrid.Visibility = Visibility.Visible;
                imageRowDefinition.Height = new GridLength(1, GridUnitType.Star);
                
                UpdateImageInfo();
                UpdateStatus("Area captured successfully");
                Console.WriteLine("Area capture completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during area capture: {ex.Message}");
                MessageBox.Show($"Error capturing area: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("Error capturing area");
                
                // Show the main window again
                this.Show();
                this.Activate();
            }
        }

        private void btnCaptureScreen_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Hide the application window temporarily
                this.Hide();
                System.Threading.Thread.Sleep(1000); // Longer delay to ensure window is completely hidden
                
                // Capture the entire screen including all monitors
                Console.WriteLine("Capturing full screen...");
                // Ensure we're capturing the entire screen, not just a portion
                _currentImage = _captureService.CaptureScreen();
                imgScreenshot.Source = _currentImage;
                
                Console.WriteLine($"imgScreenshot.Source impostato: {_currentImage?.PixelWidth}x{_currentImage?.PixelHeight}");
                Console.WriteLine($"imageDisplayGrid.Visibility: {imageDisplayGrid.Visibility}, imageRowDefinition.Height: {imageRowDefinition.Height.Value}");
                
                // Show the window again
                this.Show();
                this.Activate();
                
                // Make sure the image display area is visible
                imageDisplayGrid.Visibility = Visibility.Visible;
                imageRowDefinition.Height = new GridLength(1, GridUnitType.Star);
                
                UpdateImageInfo();
                UpdateStatus("Full screen captured successfully");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error capturing screen: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("Error capturing screen");
                
                // Show the window again in case of error
                this.Show();
                this.Activate();
            }
        }

        private void btnCaptureWindow_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Hide the application window temporarily
                this.Hide();
                System.Threading.Thread.Sleep(200); // Short delay to ensure window is hidden
                
                _currentImage = _captureService.CaptureActiveWindow();
                imgScreenshot.Source = _currentImage;
                
                // Show the window again
                this.Show();
                this.Activate();
                
                // Make sure the image display area is visible
                imageDisplayGrid.Visibility = Visibility.Visible;
                imageRowDefinition.Height = new GridLength(1, GridUnitType.Star);
                
                UpdateImageInfo();
                UpdateStatus("Window captured successfully");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error capturing window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("Error capturing window");
                
                // Show the window again in case of error
                this.Show();
                this.Activate();
            }
        }

        private void btnZoomIn_Click(object sender, RoutedEventArgs e)
        {
            UpdateZoom(ZOOM_FACTOR);
        }

        private void btnZoomOut_Click(object sender, RoutedEventArgs e)
        {
            UpdateZoom(-ZOOM_FACTOR);
        }

        private void imgScreenshot_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (Keyboard.Modifiers == ModifierKeys.Control)
            {
                if (e.Delta > 0) // Scroll up
                {
                    UpdateZoom(ZOOM_FACTOR);
                }
                else // Scroll down
                {
                    UpdateZoom(-ZOOM_FACTOR);
                }
                e.Handled = true; // Prevent the ScrollViewer from scrolling
            }
        }

        private SelectionResult? ShowElementSelectionDialog()
        {
            try
            {
                // Hide main window before showing selection
                this.Hide();

                // Create and show element selection window
                ElementSelectionWindow selectionWindow = new ElementSelectionWindow();
                bool? result = selectionWindow.ShowDialog();

                if (result == true)
                {
                    return selectionWindow.SelectedArea;
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in element selection: {ex.Message}");
                MessageBox.Show($"Error during element selection: {ex.Message}", "Selection Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
            finally
            {
                // Always ensure main window is shown again
                if (!this.IsVisible)
                {
                    this.Show();
                    this.Activate();
                }
            }
        }

        private void btnTestScroll_Click(object sender, RoutedEventArgs e)
        {
            WindowItem? selectedWindow = SelectWindowDialog();
            if (selectedWindow == null) return;

            _testWindowHandle = selectedWindow.Handle;
            _isTestMode = true;
            floatingScrollControl.Visibility = Visibility.Visible;
            UpdateStatus($"Test mode active for window: {selectedWindow.Title}. Use floating buttons to test scroll.");
        }

        private WindowItem? SelectWindowDialog()
        {
            WindowItem? selectedWindow = null;
            try
            {
                Console.WriteLine("btnAutoScroll_Click: Entered.");
                // Create a window selector dialog
                Window windowSelectorDialog = new Window
                {
                    Title = "Select Window",
                    Width = 400,
                    Height = 300,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    ResizeMode = ResizeMode.NoResize,
                    Background = new SolidColorBrush(Colors.WhiteSmoke)
                };
                
                // Create a stack panel for the content
                StackPanel mainPanel = new StackPanel
                {
                    Margin = new Thickness(10)
                };
                
                // Add a title
                TextBlock titleText = new TextBlock
                {
                    Text = "Select a window to capture:",
                    FontSize = 16,
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 10)
                };
                mainPanel.Children.Add(titleText);
                
                // Create a list box for window selection with icons
                ListBox windowListBox = new ListBox
                {
                    Height = 200,
                    Margin = new Thickness(0, 0, 0, 10)
                };
                
                // Get all open windows
                var openWindows = GetOpenWindows();
                foreach (var window in openWindows)
                {
                    var item = new WindowItem { Title = window.Value, Handle = window.Key };
                    
                    // Create a list box item with icon
                    ListBoxItem listBoxItem = new ListBoxItem();
                    StackPanel itemPanel = new StackPanel { Orientation = Orientation.Horizontal };
                    
                    // Get window icon
                    System.Drawing.Icon? icon = null;
                    try {
                        icon = GetAppIcon(window.Key);
                    } catch (Exception ex) {
                        Console.WriteLine($"Could not get icon for window {window.Value}: {ex.Message}");
                    }
                    
                    // Convert icon to image if available
                    if (icon != null)
                    {
                        using (var bitmap = icon.ToBitmap())
                        {
                            var bitmapSource = System.Windows.Interop.Imaging.CreateBitmapSourceFromHBitmap(
                                bitmap.GetHbitmap(),
                                IntPtr.Zero,
                                Int32Rect.Empty,
                                BitmapSizeOptions.FromEmptyOptions());
                            Image iconImage = new Image { Source = bitmapSource, Width = 16, Height = 16, Margin = new Thickness(0,0,5,0) };
                            itemPanel.Children.Add(iconImage);
                        }
                    }
                    
                    TextBlock windowTitleText = new TextBlock { Text = window.Value }; // Renamed to avoid conflict
                    itemPanel.Children.Add(windowTitleText);
                    listBoxItem.Content = itemPanel;
                    listBoxItem.Tag = item;
                    windowListBox.Items.Add(listBoxItem);
                }
                mainPanel.Children.Add(windowListBox);

                // Add MouseDoubleClick event handler for ListBox items
                windowListBox.MouseDoubleClick += (s, args) =>
                {
                    if (windowListBox.SelectedItem is ListBoxItem selectedItem && selectedItem.Tag is WindowItem winItem)
                    {
                        selectedWindow = winItem;
                        windowSelectorDialog.DialogResult = true;
                        windowSelectorDialog.Close();
                    }
                };
                
                // Add OK and Cancel buttons
                StackPanel buttonPanel = new StackPanel { Orientation = Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Right };
                Button okButton = new Button { Content = "OK", Width = 80, Margin = new Thickness(0,0,10,0) };
                Button cancelButton = new Button { Content = "Cancel", Width = 80 };
                buttonPanel.Children.Add(okButton);
                buttonPanel.Children.Add(cancelButton);
                mainPanel.Children.Add(buttonPanel);
                
                windowSelectorDialog.Content = mainPanel;
                
                okButton.Click += (s, args) => {
                    if (windowListBox.SelectedItem is ListBoxItem selectedItem && selectedItem.Tag is WindowItem winItem)
                    {
                        selectedWindow = winItem;
                        windowSelectorDialog.DialogResult = true;
                        windowSelectorDialog.Close();
                    }
                };
                cancelButton.Click += (s, args) => {
                    windowSelectorDialog.DialogResult = false;
                    windowSelectorDialog.Close();
                };
                
                Console.WriteLine("btnAutoScroll_Click: Window selector dialog configured. Hiding MainWindow before showing dialog.");
                // Hide main window before showing dialog to prevent it from being selected
                this.Hide();

                bool? dialogResult = windowSelectorDialog.ShowDialog();

                if (dialogResult == true)
                {
                    return selectedWindow;
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in window selection dialog: {ex.Message}");
                MessageBox.Show($"Error selecting window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }

        private void btnAutoScroll_Click(object sender, RoutedEventArgs e) // Screenshot Scroll functionality
        {
            bool overlaySuccessfullyShown = false;

            // Use new intelligent element selection
            SelectionResult? selectedArea = ShowElementSelectionDialog();

            if (selectedArea == null)
            {
                UpdateStatus("Scrolling capture cancelled.");
                // If dialog was cancelled, we might need to reshow the main window if it was hidden.
                if (!this.IsVisible)
                {
                    this.Show();
                    this.Activate();
                }
                return;
            }

            try
            {
                IntPtr hwnd = selectedArea.WindowHandle;
                Console.WriteLine($"btnAutoScroll_Click: Area selected: {selectedArea.Description}, HWND: {hwnd}, Type: {selectedArea.Type}");
                
                // The main window is already hidden. It will be shown when _scrollCaptureOverlay closes.
                // await Task.Delay(200); // Ensure main window is hidden - not strictly needed if Hide() is synchronous enough

                // Create the scroll capture overlay window with selection information
                _scrollCaptureOverlay = new ScrollCaptureOverlayWindow(hwnd, this, selectedArea);
                Console.WriteLine("btnAutoScroll_Click: ScrollCaptureOverlayWindow instance created.");

                // MainWindow is hidden. Overlay will be Topmost.
                this.Topmost = false; // Prepare MainWindow not to be Topmost when it's reshown.
                _scrollCaptureOverlay.Topmost = true;
                
                // Ensure main window is restored when overlay closes
                _scrollCaptureOverlay.Closed += (s_overlay, e_closed) => {
                    try
                    {
                        Console.WriteLine("ScrollCaptureOverlay.Closed event fired in MainWindow.");
                        _scrollCaptureOverlay = null; // Dereference
                        
                        // Attempt to show and activate the main window
                        this.Show();
                        this.Topmost = true;
                        this.Activate();
                        // this.Focus(); // Activate() usually handles focus.
                        
                        // Optional: If Topmost is not desired long-term
                        // Dispatcher.BeginInvoke(new Action(() => { this.Topmost = false; }), System.Windows.Threading.DispatcherPriority.ApplicationIdle);

                        Console.WriteLine("MainWindow.Show() and Activate() called.");
                        UpdateStatus("Scroll capture finished or cancelled.");
                    }
                    catch (Exception exClosedHandler)
                    {
                        Console.WriteLine($"CRITICAL ERROR in ScrollCaptureOverlay.Closed handler: {exClosedHandler.ToString()}");
                        MessageBox.Show($"A critical error occurred trying to restore the main window: {exClosedHandler.Message}. Please check logs.", "Restore Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        try { this.Show(); this.Activate(); } catch { /* ignore further errors here */ }
                    }
                };
                Console.WriteLine("btnAutoScroll_Click: ScrollCaptureOverlay.Closed event handler attached.");
                
                // Show the overlay without hiding main window
                _scrollCaptureOverlay.Show();
                overlaySuccessfullyShown = true;
                Console.WriteLine("btnAutoScroll_Click: ScrollCaptureOverlay.Show() called successfully.");
                UpdateStatus("Scroll capture in progress...");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during auto scroll setup (MainWindow): {ex.ToString()}"); // Log full exception
                MessageBox.Show($"Error during auto scroll setup: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("Error in auto scroll setup");
                // MainWindow is hidden. The finally block will attempt to restore it.
            }
            finally
            {
                Console.WriteLine($"btnAutoScroll_Click: Reached finally. MainWindow.IsVisible: {this.IsVisible}, overlaySuccessfullyShown: {overlaySuccessfullyShown}");
                bool mainWindowWasHiddenForThisOperation = !this.IsVisible; // Check if Hide() was called earlier in the try block

                // If MainWindow was hidden for this operation AND the overlay was *not* successfully initiated to show,
                // then this finally block must restore MainWindow.
                if (mainWindowWasHiddenForThisOperation && !overlaySuccessfullyShown)
                {
                    // This block should only execute if the dialog was shown but the overlay failed to initialize.
                    // Since the dialog is now part of a separate synchronous method, we need to adjust this logic.
                    // A simple check for visibility is sufficient.
                    if (!this.IsVisible)
                    {
                        try
                        {
                            this.Show();
                            this.Activate();
                            this.Topmost = true;
                            Console.WriteLine("btnAutoScroll_Click: MainWindow visibility restored from finally block.");
                        }
                        catch (Exception recoveryEx)
                        {
                            Console.WriteLine($"CRITICAL Error trying to recover MainWindow visibility from finally: {recoveryEx.ToString()}");
                        }
                    }
                }
                else if (mainWindowWasHiddenForThisOperation && overlaySuccessfullyShown) {
                    Console.WriteLine("btnAutoScroll_Click: MainWindow was hidden and overlay WAS successfully shown. Relying on overlay's Closed event for restoration if it's no longer visible.");
                } else if (!mainWindowWasHiddenForThisOperation) {
                    Console.WriteLine("btnAutoScroll_Click: MainWindow was not hidden for this operation. No action in finally.");
                }
                
            }
        }

        private Task<BitmapSource?> StitchImagesAsync(List<BitmapSource> images, IntPtr hwnd)
        {
            if (images == null || images.Count == 0)
            {
                Console.WriteLine("No images to stitch.");
                return Task.FromResult<BitmapSource?>(null);
            }

            if (images.Count == 1)
            {
                return Task.FromResult<BitmapSource?>(images[0]);
            }

            try
            {
                BitmapSource? currentStitchedImage = images[0];
                for (int i = 1; i < images.Count; i++)
                {
                    // Per lo scroll verso il basso, la nuova immagine va aggiunta in fondo.
                    if (currentStitchedImage == null) // Controllo aggiunto per sicurezza prima della chiamata
                    {
                        Console.WriteLine("Current stitched image is null before stitching next image. Aborting.");
                        return Task.FromResult<BitmapSource?>(null);
                    }
                    currentStitchedImage = _imageService.StitchImagesVertically(currentStitchedImage, images[i], false);
                    if (currentStitchedImage == null)
                    {
                        Console.WriteLine($"Stitching failed at image index {i}. Aborting.");
                        return Task.FromResult<BitmapSource?>(null);
                    }
                }
                return Task.FromResult<BitmapSource?>(currentStitchedImage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error stitching images: {ex.Message}");
                return Task.FromResult<BitmapSource?>(null);
            }
        }
        
        // P/Invoke declarations for window management
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool SetForegroundWindow(IntPtr hWnd);
        
        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

        private static readonly IntPtr HWND_TOPMOST = new IntPtr(-1);
        private const uint SWP_NOMOVE = 0x0002;
        private const uint SWP_NOSIZE = 0x0001;
        private const uint SWP_SHOWWINDOW = 0x0040;
        private const uint SWP_NOACTIVATE = 0x0010;
        
        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }
        
        // Method to get all open windows
        private Dictionary<IntPtr, string> GetOpenWindows()
        {
            var shellWindow = GetShellWindow();
            var windows = new Dictionary<IntPtr, string>();
            
            EnumWindows(delegate(IntPtr hWnd, IntPtr lParam) {
                if (hWnd == shellWindow) return true;
                if (!IsWindowVisible(hWnd)) return true;
                
                int length = GetWindowTextLength(hWnd);
                if (length == 0) return true;
                
                var builder = new StringBuilder(length + 1);
                GetWindowText(hWnd, builder, builder.Capacity);
                
                // Skip windows with empty titles or special windows
                string title = builder.ToString();
                if (string.IsNullOrWhiteSpace(title)) return true;
                if (title == "Program Manager") return true;
                
                // Skip this application's window
                if (title.Contains("CaptureMaster Pro")) return true;
                
                windows[hWnd] = title;
                return true;
            }, IntPtr.Zero);
            
            return windows;
        }
        
        // P/Invoke declarations for window enumeration
        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);
        
        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);
        
        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);
        
        [DllImport("user32.dll")]
        private static extern int GetWindowTextLength(IntPtr hWnd);
        
        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);
        
        [DllImport("user32.dll")]
        private static extern IntPtr GetShellWindow();
        
        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            if (_currentImage == null)
            {
                MessageBox.Show("No image to save.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Filter = "PNG Image|*.png|JPEG Image|*.jpg|BMP Image|*.bmp|All Files|*.*",
                DefaultExt = ".png",
                FileName = $"Screenshot_{DateTime.Now:yyyyMMdd_HHmmss}"
            };
            
            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    // Save the image
                    _imageService.SaveImage(_currentImage, saveFileDialog.FileName);
                    UpdateStatus($"Image saved to {saveFileDialog.FileName}");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error saving image: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    UpdateStatus("Error saving image");
                }
            }
        }
        
        private void btnCopy_Click(object sender, RoutedEventArgs e)
        {
            if (_currentImage == null)
            {
                MessageBox.Show("No image to copy.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            try
            {
                // Copy the image to clipboard
                _imageService.CopyToClipboard(_currentImage);
                UpdateStatus("Image copied to clipboard");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error copying to clipboard: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("Error copying to clipboard");
            }
        }
        
        private void btnGrayscale_Click(object sender, RoutedEventArgs e)
        {
            if (_currentImage == null) return;
            
            try
            {
                _currentImage = _imageService.ApplyGrayscaleEffect(_currentImage);
                imgScreenshot.Source = _currentImage;
                UpdateStatus("Grayscale filter applied");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error applying grayscale: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void btnRotate_Click(object sender, RoutedEventArgs e)
        {
            if (_currentImage == null) return;
            
            try
            {
                _currentImage = _imageService.Rotate(_currentImage, 90);
                imgScreenshot.Source = _currentImage;
                UpdateImageInfo();
                UpdateStatus("Image rotated 90 degrees");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error rotating image: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void btnCrop_Click(object sender, RoutedEventArgs e)
        {
            if (_currentImage == null)
            {
                MessageBox.Show("Nessuna immagine da ritagliare.", "Errore", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            try
            {
                // Crea un overlay per la selezione dell'area di ritaglio
                Grid overlayGrid = new Grid();
                overlayGrid.Background = new SolidColorBrush(Color.FromArgb(100, 0, 0, 0));
                
                // Crea un canvas per il disegno del rettangolo di selezione
                Canvas cropCanvas = new Canvas();
                cropCanvas.Background = Brushes.Transparent;
                
                // Aggiungi l'immagine corrente come sfondo
                Image backgroundImage = new Image();
                backgroundImage.Source = _currentImage;
                backgroundImage.Stretch = Stretch.None;
                
                // Aggiungi i controlli al grid principale
                overlayGrid.Children.Add(backgroundImage);
                overlayGrid.Children.Add(cropCanvas);
                
                // Crea una finestra per la selezione del ritaglio
                Window cropWindow = new Window
                {
                    Title = "Seleziona l'area da ritagliare",
                    Content = overlayGrid,
                    Width = _currentImage.PixelWidth,
                    Height = _currentImage.PixelHeight,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    ResizeMode = ResizeMode.NoResize,
                    Owner = this
                };
                
                // Variabili per la selezione
                Rectangle? selectionRect = null;
                Point startPoint = new Point();
                bool isSelecting = false;
                
                // Gestione degli eventi del mouse
                cropCanvas.MouseLeftButtonDown += (s, args) =>
                {
                    isSelecting = true;
                    startPoint = args.GetPosition(cropCanvas);
                    
                    // Crea un nuovo rettangolo di selezione
                    selectionRect = new Rectangle
                    {
                        Stroke = new SolidColorBrush(Colors.Red),
                        StrokeThickness = 2,
                        Fill = new SolidColorBrush(Color.FromArgb(50, 173, 216, 230))
                    };
                    
                    Canvas.SetLeft(selectionRect, startPoint.X);
                    Canvas.SetTop(selectionRect, startPoint.Y);
                    cropCanvas.Children.Add(selectionRect);
                };
                
                cropCanvas.MouseMove += (s, args) =>
                {
                    if (!isSelecting || selectionRect == null) return;
                    
                    var currentPoint = args.GetPosition(cropCanvas);
                    
                    // Aggiorna le dimensioni del rettangolo
                    double width = Math.Abs(currentPoint.X - startPoint.X);
                    double height = Math.Abs(currentPoint.Y - startPoint.Y);
                    
                    // Aggiorna la posizione del rettangolo
                    double left = Math.Min(startPoint.X, currentPoint.X);
                    double top = Math.Min(startPoint.Y, currentPoint.Y);
                    
                    Canvas.SetLeft(selectionRect, left);
                    Canvas.SetTop(selectionRect, top);
                    selectionRect.Width = width;
                    selectionRect.Height = height;
                };
                
                cropCanvas.MouseLeftButtonUp += (s, args) =>
                {
                    if (!isSelecting || selectionRect == null) return;
                    
                    isSelecting = false;
                    var currentPoint = args.GetPosition(cropCanvas);
                    
                    // Calcola l'area di selezione
                    double left = Math.Min(startPoint.X, currentPoint.X);
                    double top = Math.Min(startPoint.Y, currentPoint.Y);
                    double width = Math.Abs(currentPoint.X - startPoint.X);
                    double height = Math.Abs(currentPoint.Y - startPoint.Y);
                    
                    // Verifica che l'area selezionata sia valida
                    if (width < 5 || height < 5)
                    {
                        MessageBox.Show("L'area selezionata è troppo piccola. Seleziona un'area più grande.", 
                            "Selezione troppo piccola", MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }
                    
                    // Chiudi la finestra di selezione
                    cropWindow.DialogResult = true;
                    
                    // Applica il ritaglio
                    Int32Rect cropRect = new Int32Rect((int)left, (int)top, (int)width, (int)height);
                    _currentImage = _imageService.Crop(_currentImage, cropRect);
                    imgScreenshot.Source = _currentImage;
                    
                    UpdateImageInfo();
                    UpdateStatus("Immagine ritagliata con successo");
                };
                
                // Aggiungi pulsanti per confermare o annullare
                StackPanel buttonPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Bottom,
                    Margin = new Thickness(0, 0, 0, 10)
                };
                
                Button cancelButton = new Button
                {
                    Content = "Annulla",
                    Padding = new Thickness(10, 5, 10, 5),
                    Margin = new Thickness(5),
                    Background = Brushes.LightGray
                };
                
                cancelButton.Click += (s, args) =>
                {
                    cropWindow.DialogResult = false;
                };
                
                buttonPanel.Children.Add(cancelButton);
                overlayGrid.Children.Add(buttonPanel);
                
                // Mostra la finestra di selezione
                bool? result = cropWindow.ShowDialog();
                
                // Se l'utente ha annullato, non fare nulla
                if (result != true)
                {
                    UpdateStatus("Ritaglio annullato");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Errore durante il ritaglio dell'immagine: {ex.Message}", "Errore", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("Errore durante il ritaglio");
            }
        }
        
        public void UpdateStatus(string message) // Made public
        {
            if (txtStatus != null) 
            {
                txtStatus.Text = message;
            }
            Console.WriteLine($"Status: {message}");
        }
        
        private void CollapseImageArea()
        {
            // Hide the image display area
            imageDisplayGrid.Visibility = Visibility.Collapsed;
            imageRowDefinition.Height = new GridLength(0);
            floatingScrollControl.Visibility = Visibility.Collapsed;
            _currentImage = null;
            imgScreenshot.Source = null;
            txtImageSize.Text = "";
            
            // Resize window to show only toolbar
            SizeToContent = SizeToContent.WidthAndHeight;
        }
        
        private void btnCollapse_Click(object sender, RoutedEventArgs e)
        {
            CollapseImageArea();
            _isTestMode = false;
            _testWindowHandle = IntPtr.Zero;
            floatingScrollControl.Visibility = Visibility.Collapsed;

            UpdateStatus("Ready");
            // Initialize zoom
            UpdateZoom(0);
        }

private void UpdateZoom(double delta)
        {
            _currentZoom = Math.Max(0.1, _currentZoom + delta); // Prevent zooming out too much
            if (imgScreenshot != null && imgScreenshot.RenderTransform is ScaleTransform scaleTransform)
            {
                scaleTransform.ScaleX = _currentZoom;
                scaleTransform.ScaleY = _currentZoom;
            }
            if (txtImageSize != null)
            {
                txtImageSize.Text = $"Zoom: {(_currentZoom * 100):F0}%";
            }
        }
        public void UpdateImageInfo() // Made public
        {
            if (txtImageInfo != null) 
            {
                if (_currentImage != null)
                {
                    txtImageInfo.Text = $"Size: {_currentImage.PixelWidth}x{_currentImage.PixelHeight}";
                    // Ensure image display is visible if it's supposed to be and _currentImage is not null
                    if (imageDisplayGrid != null && imageDisplayGrid.Visibility == Visibility.Visible && imageRowDefinition != null)
                    {
                         imageRowDefinition.Height = new GridLength(1, GridUnitType.Star);
                    }
                }
                else
                {
                    txtImageInfo.Text = "No image loaded";
                    // If no image, ensure the display area is collapsed.
                     if (imageDisplayGrid != null && imageRowDefinition != null)
                     {
                        imageDisplayGrid.Visibility = Visibility.Collapsed;
                        imageRowDefinition.Height = new GridLength(0);
                     }
                }
            }
        }
    }

    // Helper class for User32 imports
    internal static class User32
    {
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        internal static extern IntPtr GetForegroundWindow();
    }
}
