<Window x:Class="CaptureMasterPro.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CaptureMasterPro"
        mc:Ignorable="d"
        Title="CaptureMaster Pro" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        SizeToContent="Manual"
        ResizeMode="CanResize"
        ShowInTaskbar="True"
        Topmost="False">
    <Window.Resources>
        <!-- Tool Icon Button Style -->
        <Style x:Key="ToolIconButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="IsTabStop" Value="False"/>
            <Setter Property="Focusable" Value="False"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            TextElement.FontFamily="{TemplateBinding FontFamily}"
                                            TextElement.FontSize="{TemplateBinding FontSize}"
                                            TextElement.FontWeight="{TemplateBinding FontWeight}"
                                            TextElement.Foreground="{TemplateBinding Foreground}"
                                            IsHitTestVisible="False"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E0E0E0"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#C0C0C0"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Tooltip Style -->
        <Style x:Key="CustomTooltipStyle" TargetType="ToolTip">
            <Setter Property="Background" Value="#333333"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#555555"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="HasDropShadow" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToolTip">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <StackPanel>
                                <TextBlock x:Name="TooltipTitle" 
                                           Text="{Binding Title}" 
                                           FontWeight="Bold" 
                                           Margin="0,0,0,4"/>
                                <TextBlock x:Name="TooltipDescription" 
                                           Text="{Binding Description}" 
                                           TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Floating Control Style -->
        <Style x:Key="FloatingControlStyle" TargetType="Border">
            <Setter Property="Background" Value="#333333"/>
            <Setter Property="BorderBrush" Value="#555555"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Opacity" Value="0.85"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect ShadowDepth="2" BlurRadius="5" Color="#333333" Opacity="0.5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="0" x:Name="imageRowDefinition"/>
            <RowDefinition Height="Auto"/>            
        </Grid.RowDefinitions>
        
        <!-- Top Toolbar with Icons Only -->
        <ToolBarTray Grid.Row="0" Background="#F0F0F0">
            <ToolBar Background="Transparent" BorderThickness="0">
                <!-- Capture Screen Button -->
                <Button x:Name="btnCaptureScreen" 
                        Click="btnCaptureScreen_Click" 
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M22,4H17L19,2H22M2,22H7L5,20H2M22,20H19L21,22H22M2,2H5L3,4H2M10,2H14V4H10M10,20H14V22H10M20,10H22V14H20M2,10H4V14H2M5,7H19V17H5V7Z" 
                              Fill="#444444" 
                              Width="24" 
                              Height="24" 
                              Stretch="Uniform"/>
                        <TextBlock Text="Screen" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Capture Screen" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Take a screenshot of your entire screen" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>
                
                <!-- Capture Window Button -->
                <Button x:Name="btnCaptureWindow" 
                        Click="btnCaptureWindow_Click" 
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M4,4H20V20H4V4M6,8V18H18V8H6Z" 
                              Fill="#444444" 
                              Width="24" 
                              Height="24" 
                              Stretch="Uniform"/>
                        <TextBlock Text="Window" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Capture Window" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Take a screenshot of the active window" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>
                
                <!-- Capture Area Button -->
                <Button x:Name="btnCaptureArea" 
                        Click="btnCaptureArea_Click" 
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M3,3H21V21H3V3M5,5V19H19V5H5Z" 
                              Fill="#444444" 
                              Width="24" 
                              Height="24" 
                              Stretch="Uniform"/>
                        <TextBlock Text="Area" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Capture Area" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Select and capture a specific area of the screen" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>
                
                <Separator Margin="5,0"/>
                
                <!-- Auto Scroll Capture Button -->
                <Button x:Name="btnAutoScroll" 
                        Click="btnAutoScroll_Click" 
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19M17,17H7V16H17V17M7,8H17V9H7V8M7,12H17V13H7V12Z" 
                              Fill="#444444" 
                              Width="24" 
                              Height="24" 
                              Stretch="Uniform"/>
                        <TextBlock Text="Screenshot Scroll" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Screenshot Scroll" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Capture a scrolling window or webpage" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>
                
                <Separator Margin="5,0"/>
                
                <!-- Save Button -->
                <Button x:Name="btnSave" 
                        Click="btnSave_Click" 
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z" 
                              Fill="#444444" 
                              Width="24" 
                              Height="24" 
                              Stretch="Uniform"/>
                        <TextBlock Text="Save" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Save" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Save the current screenshot" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>

                <!-- Test Scroll Button -->
                <Button x:Name="btnTestScroll"
                        Click="btnTestScroll_Click"
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H13V19H5V5H19V11H21V5C21,3.89 20.1,3 19,3M16.5,20.9L13.7,20.2L14.4,17.4L15.1,17.7C15.9,18 16.2,19 15.9,19.8L17.3,20.1L17.4,21.5L16.5,20.9M21.6,18.9L20.7,18.7C20.4,17.9 19.5,17.6 18.7,17.9L18.4,17L21.2,16.3L21.9,19.1L20.5,19.4L21.6,18.9M18.1,20.7L17.8,22.1L16.4,21.8L17.1,19L18.1,20.7M19.9,15.5C19.9,16.6 19,17.5 17.9,17.5C16.8,17.5 15.9,16.6 15.9,15.5C15.9,14.4 16.8,13.5 17.9,13.5C19,13.5 19.9,14.4 19.9,15.5Z"
                              Fill="#444444"
                              Width="24"
                              Height="24"
                              Stretch="Uniform"/>
                        <TextBlock Text="Test Scroll" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Test Scroll Function" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Select a window and use scroll buttons to test scrolling reliability." TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>

            </ToolBar>
            
            <ToolBar Background="Transparent" BorderThickness="0">
                <!-- Close/Collapse Button -->
                <Button x:Name="btnCollapse" 
                        Click="btnCollapse_Click" 
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" 
                              Fill="#444444" 
                              Width="24" 
                              Height="24" 
                              Stretch="Uniform"/>
                        <TextBlock Text="Close" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Collapse" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Return to toolbar-only mode" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>
                
                <!-- Crop Button -->
                <Button x:Name="btnCrop" 
                        Click="btnCrop_Click" 
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M7,17V1H21V17H7M9,3V15H19V3H9M3,5H5V21H19V23H3V5M11,9H17V11H11V9Z" 
                              Fill="#444444" 
                              Width="24" 
                              Height="24" 
                              Stretch="Uniform"/>
                        <TextBlock Text="Crop" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Crop" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Crop the current screenshot" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>
                
                <!-- Rotate Button -->
                <Button x:Name="btnRotate" 
                        Click="btnRotate_Click" 
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M16.89,15.5L18.31,16.89C19.21,15.73 19.76,14.39 19.93,13H17.91C17.77,13.87 17.43,14.72 16.89,15.5M13,17.9V19.92C14.39,19.75 15.74,19.21 16.9,18.31L15.46,16.87C14.71,17.41 13.87,17.76 13,17.9M19.93,11C19.76,9.61 19.21,8.27 18.31,7.11L16.89,8.53C17.43,9.28 17.77,10.13 17.91,11M15.55,5.55L11,1V4.07C7.06,4.56 4,7.92 4,12C4,16.08 7.05,19.44 11,19.93V17.91C8.16,17.43 6,14.97 6,12C6,9.03 8.16,6.57 11,6.09V10L15.55,5.55Z" 
                              Fill="#444444" 
                              Width="24" 
                              Height="24" 
                              Stretch="Uniform"/>
                        <TextBlock Text="Rotate" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Rotate" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Rotate the current screenshot" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>
                
                <!-- Grayscale Button -->
                <Button x:Name="btnGrayscale" 
                        Click="btnGrayscale_Click" 
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M19,19H5V5H19M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M13.96,12.29L11.21,15.83L9.25,13.47L6.5,17H17.5L13.96,12.29Z" 
                              Fill="#444444" 
                              Width="24" 
                              Height="24" 
                              Stretch="Uniform"/>
                        <TextBlock Text="Grayscale" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Grayscale" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Convert the screenshot to grayscale" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>

                <Separator Margin="5,0"/>

                <!-- Zoom In Button -->
                <Button x:Name="btnZoomIn"
                        Click="btnZoomIn_Click"
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M15.5,14H14.71L14.43,13.73C15.41,12.59 16,11.11 16,9.5C16,5.91 13.09,3 9.5,3S3,5.91 3,9.5C3,13.09 5.91,16 9.5,16C11.11,16 12.59,15.41 13.73,14.43L14,14.71V15.5L19,20.49L20.49,19L15.5,14ZM9.5,14C7.01,14 5,11.99 5,9.5S7.01,5 9.5,5 14,7.01 14,9.5S11.99,14 9.5,14ZM10,7H9V9H7V10H9V12H10V10H12V9H10V7Z"
                              Fill="#444444"
                              Width="24"
                              Height="24"
                              Stretch="Uniform"/>
                        <TextBlock Text="Zoom In" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Zoom In" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Zoom in on the screenshot" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>

                <!-- Zoom Out Button -->
                <Button x:Name="btnZoomOut"
                        Click="btnZoomOut_Click"
                        Style="{StaticResource ToolIconButtonStyle}">
                    <StackPanel Orientation="Vertical">
                        <Path Data="M15.5,14H14.71L14.43,13.73C15.41,12.59 16,11.11 16,9.5C16,5.91 13.09,3 9.5,3S3,5.91 3,9.5C3,13.09 5.91,16 9.5,16C11.11,16 12.59,15.41 13.73,14.43L14,14.71V15.5L19,20.49L20.49,19L15.5,14ZM9.5,14C7.01,14 5,11.99 5,9.5S7.01,5 9.5,5 14,7.01 14,9.5S11.99,14 9.5,14ZM7,9H12V10H7V9Z"
                              Fill="#444444"
                              Width="24"
                              Height="24"
                              Stretch="Uniform"/>
                        <TextBlock Text="Zoom Out" FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Style="{StaticResource CustomTooltipStyle}">
                            <ToolTip.Content>
                                <StackPanel>
                                    <TextBlock Text="Zoom Out" FontWeight="Bold" Margin="0,0,0,4" Foreground="White"/>
                                    <TextBlock Text="Zoom out of the screenshot" TextWrapping="Wrap" Foreground="White"/>
                                </StackPanel>
                            </ToolTip.Content>
                        </ToolTip>
                    </Button.ToolTip>
                </Button>
            </ToolBar>
        </ToolBarTray>
        
        <!-- Main Image Display Area (Initially Collapsed) -->
        <Grid Grid.Row="1" x:Name="imageDisplayGrid" Visibility="Collapsed">
            <ScrollViewer x:Name="screenshotScrollViewer" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
                <Image x:Name="imgScreenshot" Stretch="None" RenderOptions.BitmapScalingMode="HighQuality" MouseWheel="imgScreenshot_MouseWheel" RenderTransformOrigin="0.5,0.5">
                    <Image.RenderTransform>
                        <ScaleTransform x:Name="imageScaleTransform" ScaleX="1.0" ScaleY="1.0" />
                    </Image.RenderTransform>
                </Image>
            </ScrollViewer>
            <TextBlock x:Name="txtImageInfo" 
                       Margin="10" 
                       VerticalAlignment="Bottom" 
                       HorizontalAlignment="Left" 
                       Foreground="Black" 
                       Background="#A0FFFFFF" 
                       Padding="5"/>
            
            <!-- Floating Scroll Control (Initially Hidden) -->
            <Border x:Name="floatingScrollControl" 
                    Style="{StaticResource FloatingControlStyle}" 
                    HorizontalAlignment="Right" 
                    VerticalAlignment="Center" 
                    Margin="0,0,20,0" 
                    Visibility="Collapsed"
                    Opacity="0.7">
                <StackPanel>
                    <Button x:Name="btnScrollUp" 
                            Click="btnScrollUp_Click" 
                            Style="{StaticResource ToolIconButtonStyle}" 
                            Padding="8">
                        <Path Data="M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z" 
                              Fill="White" 
                              Width="20" 
                              Height="20" 
                              Stretch="Uniform"/>
                    </Button>
                    <Button x:Name="btnScrollDown" 
                            Click="btnScrollDown_Click" 
                            Style="{StaticResource ToolIconButtonStyle}" 
                            Padding="8">
                        <Path Data="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" 
                              Fill="White" 
                              Width="20" 
                              Height="20" 
                              Stretch="Uniform"/>
                    </Button>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="#F0F0F0" Height="22">
            <StatusBarItem>
                <TextBlock x:Name="txtStatus" Text="Ready" FontSize="11"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock x:Name="txtImageSize" Text="" FontSize="11"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
