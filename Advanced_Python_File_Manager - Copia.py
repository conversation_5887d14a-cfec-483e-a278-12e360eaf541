import sys
import os
import shutil
import filecmp
import difflib
from datetime import datetime
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QPushButton, QTableWidget, QTableWidgetItem,
    QLabel, QTextEdit, QFileDialog, QMessageBox, QFrame,
    QStackedLayout, QHeaderView, QAbstractItemView
)
from PyQt6.QtGui import QFont, QDragEnterEvent, QDropEvent, QPalette, QColor, QPainter, QPen, QBrush
from PyQt6.QtCore import Qt, QThread, QObject, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QTimer

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  WORKERS for running tasks in the background
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class ContentLoaderWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, folder_path, current_filter=None):
        super().__init__()
        self.folder_path = folder_path
        self.current_filter = current_filter
        self._is_running = True

    def run(self):
        try:
            results = []
            if self.current_filter:
                for dirpath, _, filenames in os.walk(self.folder_path):
                    if not self._is_running: break
                    for item_name in filenames:
                        if not self._is_running: break
                        ext = os.path.splitext(item_name)[1].lower()
                        if ext in self.current_filter:
                            full_path = os.path.join(dirpath, item_name)
                            relative_path = os.path.relpath(full_path, self.folder_path)
                            stats = os.stat(full_path)
                            results.append((relative_path, stats, ext, False))
                    else: continue
                    break
            else:
                for item_name in os.listdir(self.folder_path):
                    if not self._is_running: break
                    full_path = os.path.join(self.folder_path, item_name)
                    is_dir = os.path.isdir(full_path)
                    ext = os.path.splitext(item_name)[1].lower() if not is_dir else ''
                    stats = os.stat(full_path)
                    results.append((item_name, stats, ext, is_dir))
            
            if self._is_running:
                self.finished.emit(results)
        except Exception as e:
            if self._is_running:
                self.error.emit(f"Could not load content: {e}")

    def stop(self):
        self._is_running = False

class ComparisonWorker(QObject):
    finished = pyqtSignal(str)
    error = pyqtSignal(str)

    def __init__(self, path1, path2, compare_type):
        super().__init__()
        self.path1 = path1
        self.path2 = path2
        self.compare_type = compare_type

    def run(self):
        try:
            if self.compare_type == 'folder':
                comparison = filecmp.dircmp(self.path1, self.path2)
                report = f"Comparison Report\n====================\nLeft: {os.path.basename(self.path1)}\nRight: {os.path.basename(self.path2)}\n\n--- Files only in Left ---\n" + "\n".join(comparison.left_only) + "\n\n--- Files only in Right ---\n" + "\n".join(comparison.right_only) + "\n\n--- Files in Both (but different) ---\n" + "\n".join(comparison.diff_files) + "\n\n--- Files in Both (and identical) ---\n" + "\n".join(comparison.common_files)
                self.finished.emit(report)
            elif self.compare_type == 'text':
                with open(self.path1, 'r', errors='ignore') as f1, open(self.path2, 'r', errors='ignore') as f2:
                    diff = difflib.unified_diff(f1.readlines(), f2.readlines(), fromfile=os.path.basename(self.path1), tofile=os.path.basename(self.path2))
                    report = "".join(diff)
                    self.finished.emit(report if report else "The selected files are identical.")
        except Exception as e:
            self.error.emit(f"Comparison failed: {e}")

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  ANIMATED LOADING OVERLAY
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class LoadingOverlay(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setVisible(False)
        self.angle = 0
        self.timer = QTimer(self, interval=20)
        self.timer.timeout.connect(self.update_animation)

    def paintEvent(self, event):
        painter = QPainter(self)
        if not painter.isActive(): return
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(QBrush(QColor(0, 0, 0, 150)))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(self.rect())
        painter.translate(self.rect().center())
        painter.setPen(QPen(QColor("#ffffff"), 10, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        radius = min(self.width(), self.height()) / 8
        arc_rect = QRect(int(-radius), int(-radius), int(radius*2), int(radius*2))
        painter.rotate(self.angle)
        painter.drawArc(arc_rect, 0, 90 * 16)

    def update_animation(self):
        self.angle = (self.angle + 10) % 360
        self.update()

    def start_animation(self):
        self.resize(self.parent().size())
        self.setVisible(True)
        self.timer.start()

    def stop_animation(self):
        self.timer.stop()
        self.setVisible(False)

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  CustomTableWidgetItem
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class CustomTableWidgetItem(QTableWidgetItem):
    def __lt__(self, other):
        data_self = self.data(Qt.ItemDataRole.UserRole)
        data_other = other.data(Qt.ItemDataRole.UserRole)
        if data_self is not None and data_other is not None and isinstance(data_self, (int, float, datetime)):
            return data_self < data_other
        return super().__lt__(other)

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  MAIN FILE PANEL WIDGET
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class FilePanel(QFrame):
    def __init__(self, side_name, main_window):
        super().__init__()
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.side_name = side_name
        self.main_window = main_window
        self.content_type = None
        self.folder_path = None
        self.file_path = None
        self.current_filter = None
        self.thread = None
        self.worker = None
        self.is_busy = False
        panel_layout = QVBoxLayout(self)
        button_bar_layout = QHBoxLayout()
        panel_layout.addLayout(button_bar_layout)
        filter_bar_layout = QHBoxLayout()
        panel_layout.addLayout(filter_bar_layout)
        self.stacked_layout = QStackedLayout()
        self.table_widget = self._create_table_widget()
        self.text_viewer = self._create_text_viewer()
        self.add_prompt_button = self._create_prompt_button()
        self.stacked_layout.addWidget(self.table_widget)
        self.stacked_layout.addWidget(self.text_viewer)
        self.stacked_layout.addWidget(self.add_prompt_button)
        panel_layout.addLayout(self.stacked_layout)
        self.stacked_layout.setCurrentWidget(self.add_prompt_button)
        self._create_buttons(button_bar_layout, filter_bar_layout)
        self.setAcceptDrops(True)
        self.loading_overlay = LoadingOverlay(self)

    def resizeEvent(self, event):
        self.loading_overlay.resize(event.size())
        super().resizeEvent(event)

    def _create_table_widget(self):
        table = QTableWidget()
        table.setColumnCount(4)
        self.headers = ["Name", "Last Modified", "Type", "Size"]
        table.setHorizontalHeaderLabels(self.headers)
        table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        table.verticalHeader().setVisible(False)
        table.horizontalHeader().setSortIndicatorShown(True)
        table.horizontalHeader().sectionClicked.connect(self.sort_by_column)
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        table.setSortingEnabled(True)
        return table

    def _create_text_viewer(self):
        viewer = QTextEdit()
        viewer.setReadOnly(True)
        viewer.setFont(QFont("Consolas", 10))
        return viewer

    def _create_prompt_button(self):
        button = QPushButton(f"+\n\nDrag & Drop Folder or Text File")
        button.setFont(QFont("Segoe UI", 18))
        button.setStyleSheet("color: #6a717b; border: 2px dashed #6a717b;")
        button.clicked.connect(self.add_folder)
        return button

    def _create_buttons(self, button_bar, filter_bar):
        self.btn_add = QPushButton("Add Folder")
        self.btn_add.clicked.connect(self.add_folder)
        button_bar.addWidget(self.btn_add)
        self.btn_save = QPushButton("Save List")
        self.btn_save.clicked.connect(self.save_file_list)
        button_bar.addWidget(self.btn_save)
        move_direction = "->" if self.side_name == "Left" else "<-"
        self.btn_move = QPushButton(f"Move {move_direction}")
        self.btn_move.clicked.connect(self.move_files)
        button_bar.addWidget(self.btn_move)
        filter_label = QLabel("Filters:")
        filter_label.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
        filter_bar.addWidget(filter_label)
        self.btn_all = QPushButton("All Files")
        self.btn_all.clicked.connect(lambda: self.filter_files(None))
        filter_bar.addWidget(self.btn_all)
        self.btn_img = QPushButton("Images")
        self.btn_img.clicked.connect(lambda: self.filter_files(['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp']))
        filter_bar.addWidget(self.btn_img)
        self.btn_doc = QPushButton("Documents")
        self.btn_doc.clicked.connect(lambda: self.filter_files(['.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.odt']))
        filter_bar.addWidget(self.btn_doc)
        self.btn_aud = QPushButton("Audio")
        self.btn_aud.clicked.connect(lambda: self.filter_files(['.mp3', '.wav', '.ogg', '.flac', '.m4a']))
        filter_bar.addWidget(self.btn_aud)
        self.btn_vid = QPushButton("Video")
        self.btn_vid.clicked.connect(lambda: self.filter_files(['.mp4', '.mkv', '.avi', '.mov', '.wmv']))
        filter_bar.addWidget(self.btn_vid)
        filter_bar.addStretch()

    # --- THIS IS THE ROBUST, CORRECTED THREADING LOGIC ---
    def load_folder(self, folder_path, filter_ext=None):
        if not os.path.isdir(folder_path) or self.is_busy:
            return

        self.is_busy = True
        self.main_window.update_button_states()
        self.loading_overlay.start_animation()
        
        # Stop and clean up any previous thread gracefully
        self.cleanup_thread()

        self.folder_path = folder_path
        self.content_type = 'folder'
        self.file_path = None
        self.current_filter = filter_ext

        # Create and manage the new thread
        self.thread = QThread()
        self.worker = ContentLoaderWorker(self.folder_path, self.current_filter)
        self.worker.moveToThread(self.thread)

        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_loading_finished)
        self.worker.error.connect(self.on_loading_error)
        
        # When worker is done, it tells the thread to quit.
        # This will eventually emit thread.finished signal for cleanup.
        self.worker.finished.connect(self.thread.quit)
        self.worker.error.connect(self.thread.quit)
        
        self.thread.start()

    def on_loading_finished(self, results):
        self.table_widget.setSortingEnabled(False)
        self.table_widget.setRowCount(0)
        for name, stats, ext, is_dir in results:
            self.add_file_to_table(name, stats, ext, is_dir)
        self.table_widget.setSortingEnabled(True)
        self.stacked_layout.setCurrentWidget(self.table_widget)
        self.loading_overlay.stop_animation()
        self.is_busy = False
        self.main_window.update_button_states()

    def on_loading_error(self, err_msg):
        QMessageBox.critical(self, "Error", err_msg)
        self.loading_overlay.stop_animation()
        self.is_busy = False
        self.main_window.update_button_states()
    
    def cleanup_thread(self):
        if self.thread and self.thread.isRunning():
            self.worker.stop()
            self.thread.quit()
            self.thread.wait(5000) # Wait up to 5 seconds for clean exit
        self.worker = None
        self.thread = None

    def load_text_file(self, file_path):
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            self.text_viewer.setPlainText(content)
            self.content_type = 'file'
            self.folder_path = None
            self.file_path = file_path
            self.stacked_layout.setCurrentWidget(self.text_viewer)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Could not read file:\n{e}")
            self.stacked_layout.setCurrentWidget(self.add_prompt_button)
            self.content_type = None
        self.main_window.update_button_states()

    def add_file_to_table(self, name, stats, ext, is_dir):
        mod_time = datetime.fromtimestamp(stats.st_mtime)
        size_bytes = stats.st_size
        name_item = CustomTableWidgetItem(name)
        mod_time_item = CustomTableWidgetItem(mod_time.strftime('%Y-%m-%d %H:%M'))
        type_item = CustomTableWidgetItem("Folder" if is_dir else f"{ext.upper()} File")
        size_item = CustomTableWidgetItem("" if is_dir else self.format_size(size_bytes))
        name_item.setData(Qt.ItemDataRole.UserRole, name)
        mod_time_item.setData(Qt.ItemDataRole.UserRole, mod_time)
        type_item.setData(Qt.ItemDataRole.UserRole, type_item.text())
        size_item.setData(Qt.ItemDataRole.UserRole, size_bytes if not is_dir else -1)
        row_position = self.table_widget.rowCount()
        self.table_widget.insertRow(row_position)
        self.table_widget.setItem(row_position, 0, name_item)
        self.table_widget.setItem(row_position, 1, mod_time_item)
        self.table_widget.setItem(row_position, 2, type_item)
        self.table_widget.setItem(row_position, 3, size_item)

    def format_size(self, size_bytes):
        if size_bytes == 0: return "0 B"
        size_names = ("B", "KB", "MB", "GB", "TB")
        i = int(size_bytes).bit_length() // 10
        power = 1024 ** i
        return f"{round(size_bytes / power, 2)} {size_names[i]}"

    def sort_by_column(self, col_index):
        self.table_widget.sortItems(col_index, self.table_widget.horizontalHeader().sortIndicatorOrder())

    def filter_files(self, extensions):
        if self.folder_path: self.load_folder(self.folder_path, filter_ext=extensions)

    def add_folder(self):
        folder_path = QFileDialog.getExistingDirectory(self, "Select a Folder")
        if folder_path: self.load_folder(folder_path)
    
    def save_file_list(self):
        if self.content_type != 'folder' or self.table_widget.rowCount() == 0:
            QMessageBox.warning(self, "Warning", "Please load a folder with content to save its list.")
            return
        current_folder_path = self.folder_path
        current_folder_name = os.path.basename(os.path.normpath(current_folder_path))
        parent_folder_path = os.path.dirname(current_folder_path)
        parent_folder_name = os.path.basename(parent_folder_path)
        sanitized_current = current_folder_name.replace(' ', '_')
        sanitized_parent = parent_folder_name.replace(' ', '_')
        default_name = f"{sanitized_parent}-{sanitized_current}-list.txt" if sanitized_parent else f"{sanitized_current}-list.txt"
        try:
            script_dir = os.path.dirname(os.path.realpath(__file__))
            save_dir = os.path.join(script_dir, "save_list")
            os.makedirs(save_dir, exist_ok=True)
        except Exception:
            save_dir = ""
        full_default_path = os.path.join(save_dir, default_name)
        file_path, _ = QFileDialog.getSaveFileName(self, "Save File List", full_default_path, "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("\t".join(self.headers) + "\n")
                    for row in range(self.table_widget.rowCount()):
                        row_data = [self.table_widget.item(row, col).text() for col in range(self.table_widget.columnCount())]
                        f.write("\t".join(row_data) + "\n")
                QMessageBox.information(self, "Success", "File list saved successfully.")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Could not save file:\n{e}")

    def get_selected_filenames(self):
        selected_rows = sorted(list(set(index.row() for index in self.table_widget.selectedIndexes())))
        return [self.table_widget.item(row, 0).text() for row in selected_rows]

    def move_files(self):
        other_panel = self.main_window.right_panel if self.side_name == "Left" else self.main_window.left_panel
        if not self.content_type == 'folder' or not other_panel.content_type == 'folder':
            QMessageBox.warning(self, "Warning", "Both panels must be in folder mode to move files.")
            return
        filenames_to_move = self.get_selected_filenames()
        if not filenames_to_move:
            QMessageBox.warning(self, "Warning", "No files selected to move.")
            return
        for filename in filenames_to_move:
            source_path = os.path.join(self.folder_path, filename)
            destination_filename = os.path.basename(filename) 
            dest_path = os.path.join(other_panel.folder_path, destination_filename)
            try: shutil.move(source_path, dest_path)
            except Exception as e: QMessageBox.critical(self, "Error", f"Could not move {filename}:\n{e}")
        self.load_folder(self.folder_path, self.current_filter)
        other_panel.load_folder(other_panel.folder_path, other_panel.current_filter)

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls(): event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        for url in event.mimeData().urls():
            if url.isLocalFile():
                path = url.toLocalFile()
                if os.path.isdir(path):
                    self.load_folder(path)
                    break
                elif os.path.isfile(path) and path.lower().endswith('.txt'):
                    self.load_text_file(path)
                    break

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  MAIN WINDOW
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class ModernFileManager(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Advanced Python File Manager")
        self.setGeometry(50, 50, 1900, 1000)
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)

        self.left_panel = FilePanel("Left", self)
        self.right_panel = FilePanel("Right", self)
        
        main_layout.addWidget(self.left_panel, 5)
        self.create_center_panel(main_layout)
        main_layout.addWidget(self.right_panel, 5)
        
        self.update_button_states()

    def create_center_panel(self, layout):
        center_frame = QFrame()
        center_layout = QVBoxLayout(center_frame)
        center_layout.addStretch()

        self.btn_compare_folders = QPushButton("Compare\nFolders")
        self.btn_compare_folders.setFont(QFont("Segoe UI", 10))
        self.btn_compare_folders.clicked.connect(self.compare_folders)
        center_layout.addWidget(self.btn_compare_folders)

        self.btn_compare_text = QPushButton("Compare\nText Files")
        self.btn_compare_text.setFont(QFont("Segoe UI", 10))
        self.btn_compare_text.clicked.connect(self.compare_text_files)
        center_layout.addWidget(self.btn_compare_text)
        
        center_layout.addStretch()
        layout.addWidget(center_frame, 1)

    def update_button_states(self):
        is_folder_mode_both = self.left_panel.content_type == 'folder' and self.right_panel.content_type == 'folder'
        is_file_mode_both = self.left_panel.content_type == 'file' and self.right_panel.content_type == 'file'
        
        self.btn_compare_folders.setEnabled(is_folder_mode_both and not self.left_panel.is_busy and not self.right_panel.is_busy)
        self.btn_compare_text.setEnabled(is_file_mode_both and not self.left_panel.is_busy and not self.right_panel.is_busy)
        
        self.left_panel.btn_move.setEnabled(is_folder_mode_both)
        self.right_panel.btn_move.setEnabled(is_folder_mode_both)

        is_left_folder = self.left_panel.content_type == 'folder'
        self.left_panel.btn_save.setEnabled(is_left_folder)
        for btn in [self.left_panel.btn_all, self.left_panel.btn_img, self.left_panel.btn_doc, self.left_panel.btn_aud, self.left_panel.btn_vid]:
            btn.setEnabled(is_left_folder)

        is_right_folder = self.right_panel.content_type == 'folder'
        self.right_panel.btn_save.setEnabled(is_right_folder)
        for btn in [self.right_panel.btn_all, self.right_panel.btn_img, self.right_panel.btn_doc, self.right_panel.btn_aud, self.right_panel.btn_vid]:
            btn.setEnabled(is_right_folder)

    def run_comparison(self, compare_type):
        self.left_panel.loading_overlay.start_animation()
        self.right_panel.loading_overlay.start_animation()
        
        path1 = self.left_panel.folder_path if compare_type == 'folder' else self.left_panel.file_path
        path2 = self.right_panel.folder_path if compare_type == 'folder' else self.right_panel.file_path

        self.thread = QThread()
        self.worker = ComparisonWorker(path1, path2, compare_type)
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_comparison_finished)
        self.worker.error.connect(self.on_comparison_error)
        self.thread.finished.connect(self.thread.deleteLater)
        self.thread.start()

    def compare_folders(self):
        self.run_comparison('folder')

    def compare_text_files(self):
        self.run_comparison('text')

    def on_comparison_finished(self, report):
        self.left_panel.loading_overlay.stop_animation()
        self.right_panel.loading_overlay.stop_animation()
        show_report("Comparison Report", report)
        if hasattr(self, 'worker') and self.worker is not None:
            self.worker.deleteLater()

    def on_comparison_error(self, err_msg):
        self.left_panel.loading_overlay.stop_animation()
        self.right_panel.loading_overlay.stop_animation()
        QMessageBox.critical(self, "Error", err_msg)

def show_report(title, report_text):
    report_window = QMainWindow()
    report_window.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
    report_window.setWindowTitle(title)
    report_window.setGeometry(250, 250, 800, 600)
    text_edit = QTextEdit()
    text_edit.setFont(QFont("Consolas", 10))
    text_edit.setPlainText(report_text)
    text_edit.setReadOnly(True)
    report_window.setCentralWidget(text_edit)
    report_window.show()

def setup_dark_theme(app):
    app.setStyle("Fusion")
    dark_palette = QPalette()
    dark_palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
    dark_palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.white)
    dark_palette.setColor(QPalette.ColorRole.Base, QColor(35, 35, 35))
    dark_palette.setColor(QPalette.ColorRole.AlternateBase, QColor(45, 45, 45))
    dark_palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(53, 53, 53))
    dark_palette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.white)
    dark_palette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.white)
    dark_palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
    dark_palette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.white)
    dark_palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.red)
    dark_palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
    dark_palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
    dark_palette.setColor(QPalette.ColorRole.HighlightedText, QColor(25, 25, 25))
    app.setPalette(dark_palette)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    setup_dark_theme(app)
    window = ModernFileManager()
    window.show()
    sys.exit(app.exec())