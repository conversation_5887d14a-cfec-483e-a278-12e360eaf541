{"format": 1, "restore": {"C:\\AI-Programmi\\CaptureMaster_Pro\\CaptureMasterPro.csproj": {}}, "projects": {"C:\\AI-Programmi\\CaptureMaster_Pro\\CaptureMasterPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\AI-Programmi\\CaptureMaster_Pro\\CaptureMasterPro.csproj", "projectName": "CaptureMasterPro", "projectPath": "C:\\AI-Programmi\\CaptureMaster_Pro\\CaptureMasterPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\AI-Programmi\\CaptureMaster_Pro\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}