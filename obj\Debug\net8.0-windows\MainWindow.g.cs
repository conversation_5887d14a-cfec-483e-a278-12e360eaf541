﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0C4AF05A5137906F7797F4D96FD93C4F01AB3F48"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CaptureMasterPro;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CaptureMasterPro {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 103 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition imageRowDefinition;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCaptureScreen;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCaptureWindow;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCaptureArea;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAutoScroll;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSave;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTestScroll;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCollapse;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCrop;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRotate;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnGrayscale;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnZoomIn;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnZoomOut;
        
        #line default
        #line hidden
        
        
        #line 410 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid imageDisplayGrid;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer screenshotScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image imgScreenshot;
        
        #line default
        #line hidden
        
        
        #line 414 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.ScaleTransform imageScaleTransform;
        
        #line default
        #line hidden
        
        
        #line 418 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtImageInfo;
        
        #line default
        #line hidden
        
        
        #line 427 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border floatingScrollControl;
        
        #line default
        #line hidden
        
        
        #line 435 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnScrollUp;
        
        #line default
        #line hidden
        
        
        #line 445 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnScrollDown;
        
        #line default
        #line hidden
        
        
        #line 462 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtStatus;
        
        #line default
        #line hidden
        
        
        #line 466 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtImageSize;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.17.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CaptureMasterPro;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.17.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.imageRowDefinition = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 2:
            this.btnCaptureScreen = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\MainWindow.xaml"
            this.btnCaptureScreen.Click += new System.Windows.RoutedEventHandler(this.btnCaptureScreen_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnCaptureWindow = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\MainWindow.xaml"
            this.btnCaptureWindow.Click += new System.Windows.RoutedEventHandler(this.btnCaptureWindow_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.btnCaptureArea = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\..\MainWindow.xaml"
            this.btnCaptureArea.Click += new System.Windows.RoutedEventHandler(this.btnCaptureArea_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnAutoScroll = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\..\MainWindow.xaml"
            this.btnAutoScroll.Click += new System.Windows.RoutedEventHandler(this.btnAutoScroll_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.btnSave = ((System.Windows.Controls.Button)(target));
            
            #line 212 "..\..\..\MainWindow.xaml"
            this.btnSave.Click += new System.Windows.RoutedEventHandler(this.btnSave_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btnTestScroll = ((System.Windows.Controls.Button)(target));
            
            #line 236 "..\..\..\MainWindow.xaml"
            this.btnTestScroll.Click += new System.Windows.RoutedEventHandler(this.btnTestScroll_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.btnCollapse = ((System.Windows.Controls.Button)(target));
            
            #line 263 "..\..\..\MainWindow.xaml"
            this.btnCollapse.Click += new System.Windows.RoutedEventHandler(this.btnCollapse_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.btnCrop = ((System.Windows.Controls.Button)(target));
            
            #line 287 "..\..\..\MainWindow.xaml"
            this.btnCrop.Click += new System.Windows.RoutedEventHandler(this.btnCrop_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnRotate = ((System.Windows.Controls.Button)(target));
            
            #line 311 "..\..\..\MainWindow.xaml"
            this.btnRotate.Click += new System.Windows.RoutedEventHandler(this.btnRotate_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnGrayscale = ((System.Windows.Controls.Button)(target));
            
            #line 335 "..\..\..\MainWindow.xaml"
            this.btnGrayscale.Click += new System.Windows.RoutedEventHandler(this.btnGrayscale_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btnZoomIn = ((System.Windows.Controls.Button)(target));
            
            #line 361 "..\..\..\MainWindow.xaml"
            this.btnZoomIn.Click += new System.Windows.RoutedEventHandler(this.btnZoomIn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.btnZoomOut = ((System.Windows.Controls.Button)(target));
            
            #line 385 "..\..\..\MainWindow.xaml"
            this.btnZoomOut.Click += new System.Windows.RoutedEventHandler(this.btnZoomOut_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.imageDisplayGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 15:
            this.screenshotScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 16:
            this.imgScreenshot = ((System.Windows.Controls.Image)(target));
            
            #line 412 "..\..\..\MainWindow.xaml"
            this.imgScreenshot.MouseWheel += new System.Windows.Input.MouseWheelEventHandler(this.imgScreenshot_MouseWheel);
            
            #line default
            #line hidden
            return;
            case 17:
            this.imageScaleTransform = ((System.Windows.Media.ScaleTransform)(target));
            return;
            case 18:
            this.txtImageInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.floatingScrollControl = ((System.Windows.Controls.Border)(target));
            return;
            case 20:
            this.btnScrollUp = ((System.Windows.Controls.Button)(target));
            
            #line 436 "..\..\..\MainWindow.xaml"
            this.btnScrollUp.Click += new System.Windows.RoutedEventHandler(this.btnScrollUp_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.btnScrollDown = ((System.Windows.Controls.Button)(target));
            
            #line 446 "..\..\..\MainWindow.xaml"
            this.btnScrollDown.Click += new System.Windows.RoutedEventHandler(this.btnScrollDown_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.txtStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.txtImageSize = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

