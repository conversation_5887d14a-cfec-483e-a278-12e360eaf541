using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

namespace CaptureMasterPro.Services
{
    public class AutoScrollService
    {
        private const int WM_VSCROLL = 0x115;
        private const int WM_MOUSEWHEEL = 0x020A;
        private const int SB_LINEUP = 0;
        private const int SB_LINEDOWN = 1;
        private const int SB_PAGEUP = 2;
        private const int SB_PAGEDOWN = 3;
        private const int WHEEL_DELTA = 120;
        
        // Virtual key codes
        private const ushort VK_UP = 0x26;
        private const ushort VK_DOWN = 0x28;
        private const ushort VK_PRIOR = 0x21; // Page Up
        private const ushort VK_NEXT = 0x22;  // Page Down
        private const ushort VK_SPACE = 0x20; // Space key
        
        // Input types
        private const int INPUT_MOUSE = 0;
        private const int INPUT_KEYBOARD = 1;
        private const int KEYEVENTF_KEYUP = 0x0002;
        private const int MOUSEEVENTF_LEFTDOWN = 0x0002;
        private const int MOUSEEVENTF_LEFTUP = 0x0004;
        private const int MOUSEEVENTF_WHEEL = 0x0800;

        [DllImport("user32.dll")]
        private static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);
        
        [DllImport("user32.dll")]
        private static extern bool PostMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        [DllImport("user32.dll")]
        public static extern IntPtr GetForegroundWindow();
        
        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);
        
        [DllImport("user32.dll")]
        private static extern uint SendInput(uint nInputs, INPUT[] pInputs, int cbSize);
        
        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);
        
        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);
        
        [DllImport("user32.dll")]
        private static extern bool SetCursorPos(int x, int y);
        
        [DllImport("user32.dll")]
        private static extern bool ScreenToClient(IntPtr hWnd, ref POINT lpPoint);
        
        [DllImport("user32.dll")]
        private static extern bool GetScrollInfo(IntPtr hwnd, int fnBar, ref SCROLLINFO lpsi);
        
        [DllImport("user32.dll")]
        private static extern int GetScrollPos(IntPtr hWnd, int nBar);
        
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool IsIconic(IntPtr hWnd); // Checks if window is minimized

        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        private const int SW_RESTORE = 9;

        [StructLayout(LayoutKind.Sequential)]
        private struct POINT
        {
            public int X;
            public int Y;
        }
        
        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }
        
        [StructLayout(LayoutKind.Sequential)]
        private struct INPUT
        {
            public int type;
            public InputUnion u;
        }
        
        [StructLayout(LayoutKind.Explicit)]
        private struct InputUnion
        {
            [FieldOffset(0)] public MOUSEINPUT mi;
            [FieldOffset(0)] public KEYBDINPUT ki;
        }
        
        [StructLayout(LayoutKind.Sequential)]
        private struct MOUSEINPUT
        {
            public int dx;
            public int dy;
            public uint mouseData;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }
        
        [StructLayout(LayoutKind.Sequential)]
        private struct KEYBDINPUT
        {
            public ushort wVk;
            public ushort wScan;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }
        
        [StructLayout(LayoutKind.Sequential)]
        private struct SCROLLINFO
        {
            public uint cbSize;
            public uint fMask;
            public int nMin;
            public int nMax;
            public uint nPage;
            public int nPos;
            public int nTrackPos;
        }

        private enum ScrollInfoMask : uint
        {
            SIF_RANGE = 0x1,
            SIF_PAGE = 0x2,
            SIF_POS = 0x4,
            SIF_DISABLENOSCROLL = 0x8,
            SIF_TRACKPOS = 0x10,
            SIF_ALL = (SIF_RANGE | SIF_PAGE | SIF_POS | SIF_TRACKPOS)
        }

        private enum ScrollBarDirection : int
        {
            SB_HORZ = 0,
            SB_VERT = 1,
            SB_CTL = 2,
            SB_BOTH = 3
        }

        public bool ScrollUp(IntPtr hwnd = default)
        {
            if (hwnd == default)
                hwnd = GetForegroundWindow();
            
            int initialScrollPos = GetCurrentScrollPosition(hwnd);
            
            // Try multiple methods for scrolling up
            bool scrolled = TryScrollMessage(hwnd, true);
            if (!scrolled)
            {
                scrolled = TryMouseWheelScroll(hwnd, true);
                if (!scrolled)
                {
                    scrolled = TryKeyboardScroll(hwnd, true);
                }
            }
            
            // Wait a moment and check if scroll position actually changed
            System.Threading.Thread.Sleep(100);
            int newScrollPos = GetCurrentScrollPosition(hwnd);
            bool actuallyScrolled = newScrollPos != initialScrollPos;
            
            Console.WriteLine($"ScrollUp: Initial={initialScrollPos}, New={newScrollPos}, Changed={actuallyScrolled}");
            return scrolled; // Return true if any scroll attempt was made, as GetCurrentScrollPosition might not be reliable.
        }

        public bool ScrollDown(IntPtr hwnd = default)
        {
            if (hwnd == default)
                hwnd = GetForegroundWindow();
            
            int initialScrollPos = GetCurrentScrollPosition(hwnd);
            
            // Try multiple methods for scrolling down
            bool scrolled = TryScrollMessage(hwnd, false);
            if (!scrolled)
            {
                scrolled = TryMouseWheelScroll(hwnd, false);
                if (!scrolled)
                {
                    scrolled = TryKeyboardScroll(hwnd, false);
                }
            }
            
            // Wait a moment and check if scroll position actually changed
            System.Threading.Thread.Sleep(100);
            int newScrollPos = GetCurrentScrollPosition(hwnd);
            bool actuallyScrolled = newScrollPos != initialScrollPos;
            
            Console.WriteLine($"ScrollDown: Initial={initialScrollPos}, New={newScrollPos}, Changed={actuallyScrolled}");
            return scrolled; // Return true if any scroll attempt was made, as GetCurrentScrollPosition might not be reliable.
        }
        
        private bool TryScrollMessage(IntPtr hwnd, bool scrollUp)
        {
            try
            {
                // Ensure window is active
                SetForegroundWindow(hwnd);
                System.Threading.Thread.Sleep(50);
                
                IntPtr result = SendMessage(hwnd, WM_VSCROLL, 
                    (IntPtr)(scrollUp ? SB_LINEUP : SB_LINEDOWN), IntPtr.Zero);
                return result != IntPtr.Zero;
            }
            catch
            {
                return false;
            }
        }
        
        private bool TryMouseWheelScroll(IntPtr hwnd, bool scrollUp)
        {
            try
            {
                // Ensure window is active and focused
                SetForegroundWindow(hwnd);
                System.Threading.Thread.Sleep(200); // Increased delay

                // Save current cursor position and move to window center
                GetCursorPos(out POINT originalPos);
                bool cursorMoved = false;
                if (GetWindowRect(hwnd, out RECT rect))
                {
                    int centerX = (rect.Left + rect.Right) / 2;
                    int centerY = (rect.Top + rect.Bottom) / 2;
                    SetCursorPos(centerX, centerY);
                    cursorMoved = true;
                    System.Threading.Thread.Sleep(50); // Give a moment for cursor move to register
                }

                // Send wheel events without moving cursor
                int scrollAmount = scrollUp ? WHEEL_DELTA : -WHEEL_DELTA; // Standard wheel delta

                INPUT input = new INPUT();
                input.type = INPUT_MOUSE;
                input.u.mi.dx = 0;
                input.u.mi.dy = 0;
                input.u.mi.mouseData = (uint)scrollAmount;
                input.u.mi.dwFlags = MOUSEEVENTF_WHEEL;
                input.u.mi.time = 0;
                input.u.mi.dwExtraInfo = IntPtr.Zero;
                
                uint result = SendInput(1, new INPUT[] { input }, Marshal.SizeOf(typeof(INPUT)));
                System.Threading.Thread.Sleep(50); // Short delay after input

                // Restore cursor position
                if (cursorMoved)
                {
                    SetCursorPos(originalPos.X, originalPos.Y);
                }

                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TryMouseWheelScroll: Exception - {ex.Message}");
                return false;
            }
        }
        
        private bool TryKeyboardScroll(IntPtr hwnd, bool scrollUp)
        {
            try
            {
                // Ensure window is active and has focus
                SetForegroundWindow(hwnd);
                System.Threading.Thread.Sleep(200); // Increased delay
                
                // Click on the window to ensure it has focus
                if (GetWindowRect(hwnd, out RECT rect))
                {
                    int centerX = (rect.Left + rect.Right) / 2;
                    int centerY = (rect.Top + rect.Bottom) / 2;
                    
                    // Save current cursor position
                    GetCursorPos(out POINT originalPos);
                    
                    // Move to center and click
                    SetCursorPos(centerX, centerY);
                    System.Threading.Thread.Sleep(50);
                    
                    // Simulate mouse click to ensure focus
                    INPUT[] clickInputs = new INPUT[2];
                    
                    // Mouse down
                    clickInputs[0] = new INPUT
                    {
                        type = INPUT_MOUSE,
                        u = new InputUnion
                        {
                            mi = new MOUSEINPUT
                            {
                                dx = 0,
                                dy = 0,
                                mouseData = 0,
                                dwFlags = MOUSEEVENTF_LEFTDOWN,
                                time = 0,
                                dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };
                    
                    // Mouse up
                    clickInputs[1] = new INPUT
                    {
                        type = INPUT_MOUSE,
                        u = new InputUnion
                        {
                            mi = new MOUSEINPUT
                            {
                                dx = 0,
                                dy = 0,
                                mouseData = 0,
                                dwFlags = MOUSEEVENTF_LEFTUP,
                                time = 0,
                                dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };
                    
                    SendInput(2, clickInputs, Marshal.SizeOf(typeof(INPUT)));
                    System.Threading.Thread.Sleep(200); // Increased delay
                    
                    // Restore cursor position
                    SetCursorPos(originalPos.X, originalPos.Y);
                }
                
                // Try multiple keyboard methods for better compatibility
                bool success = false;
                
                // Method 1: Arrow keys (for basic scrolling)
                ushort arrowKey = scrollUp ? VK_UP : VK_DOWN;
                success |= SendKeySequence(arrowKey, 10); // Send 10 times
                
                // Method 2: Page Up/Down keys (for larger scrolling)
                ushort pageKey = scrollUp ? VK_PRIOR : VK_NEXT;
                success |= SendKeySequence(pageKey, 1); // Send once
                
                // Method 3: Space key for page down (common in browsers)
                if (!scrollUp)
                {
                    success |= SendKeySequence(VK_SPACE, 1);
                }
                
                return success;
            }
            catch
            {
                return false;
            }
        }
        
        private bool SendKeySequence(ushort key, int repeatCount)
        {
            try
            {
                for (int i = 0; i < repeatCount; i++)
                {
                    INPUT[] inputs = new INPUT[2];
                    
                    // Key down
                    inputs[0] = new INPUT
                    {
                        type = INPUT_KEYBOARD,
                        u = new InputUnion
                        {
                            ki = new KEYBDINPUT
                            {
                                wVk = key,
                                wScan = 0,
                                dwFlags = 0,
                                time = 0,
                                dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };
                    
                    // Key up
                    inputs[1] = new INPUT
                    {
                        type = INPUT_KEYBOARD,
                        u = new InputUnion
                        {
                            ki = new KEYBDINPUT
                            {
                                wVk = key,
                                wScan = 0,
                                dwFlags = KEYEVENTF_KEYUP,
                                time = 0,
                                dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };
                    
                    uint result = SendInput(2, inputs, Marshal.SizeOf(typeof(INPUT)));
                    if (result == 0) return false;
                    
                    System.Threading.Thread.Sleep(100); // Increased delay between key presses
                }
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        public int GetCurrentScrollPosition(IntPtr hwnd)
        {
            try
            {
                // Try to get vertical scroll position
                int scrollPos = GetScrollPos(hwnd, (int)ScrollBarDirection.SB_VERT);
                Console.WriteLine($"GetCurrentScrollPosition: GetScrollPos returned {scrollPos}");

                // If that doesn't work, try getting scroll info
                SCROLLINFO si = new SCROLLINFO();
                si.cbSize = (uint)Marshal.SizeOf(si);
                si.fMask = (uint)ScrollInfoMask.SIF_POS | (uint)ScrollInfoMask.SIF_RANGE | (uint)ScrollInfoMask.SIF_PAGE;
                
                if (GetScrollInfo(hwnd, (int)ScrollBarDirection.SB_VERT, ref si))
                {
                    Console.WriteLine($"GetCurrentScrollPosition: GetScrollInfo - nPos: {si.nPos}, nMin: {si.nMin}, nMax: {si.nMax}, nPage: {si.nPage}");
                    // Prioritize nPos from GetScrollInfo if available and seems valid
                    if (si.nPos != 0 || scrollPos == 0) // If GetScrollPos was 0, or GetScrollInfo gives a non-zero pos
                    {
                        scrollPos = si.nPos;
                    }
                }
                
                return scrollPos;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetCurrentScrollPosition: Error - {ex.Message}");
                return 0;
            }
        }

        public bool PageUp(IntPtr hwnd = default)
        {
            if (hwnd == default) hwnd = GetForegroundWindow();
            Console.WriteLine($"PageUp: Target HWND: {hwnd}");

            // Get initial scroll position to verify actual scrolling occurred
            int initialPos = GetCurrentScrollPosition(hwnd);
            Console.WriteLine($"PageUp: Initial scroll position: {initialPos}");

            // Try methods in order of likely success for modern apps.
            bool methodSucceeded = false;

            if (TryKeyboardPageScroll(hwnd, true))
            {
                methodSucceeded = true;
                Console.WriteLine("PageUp: TryKeyboardPageScroll reported success");
            }
            else if (TryMouseWheelPageScroll(hwnd, true))
            {
                methodSucceeded = true;
                Console.WriteLine("PageUp: TryMouseWheelPageScroll reported success");
            }
            else if (TryPageScrollMessage(hwnd, true))
            {
                methodSucceeded = true;
                Console.WriteLine("PageUp: TryPageScrollMessage reported success");
            }

            if (methodSucceeded)
            {
                // Wait for scroll to complete and verify position changed
                System.Threading.Thread.Sleep(200);
                int newPos = GetCurrentScrollPosition(hwnd);
                Console.WriteLine($"PageUp: New scroll position: {newPos}");

                bool actuallyScrolled = newPos != initialPos;
                Console.WriteLine($"PageUp: Actually scrolled: {actuallyScrolled}");
                return actuallyScrolled;
            }

            Console.WriteLine($"PageUp: All scroll methods failed.");
            return false;
        }

        public bool PageDown(IntPtr hwnd = default)
        {
            if (hwnd == default) hwnd = GetForegroundWindow();
            Console.WriteLine($"PageDown: Target HWND: {hwnd}");

            // Get initial scroll position to verify actual scrolling occurred
            int initialPos = GetCurrentScrollPosition(hwnd);
            Console.WriteLine($"PageDown: Initial scroll position: {initialPos}");

            // Try methods in order of likely success for modern apps.
            bool methodSucceeded = false;

            if (TryKeyboardPageScroll(hwnd, false))
            {
                methodSucceeded = true;
                Console.WriteLine("PageDown: TryKeyboardPageScroll reported success");
            }
            else if (TryMouseWheelPageScroll(hwnd, false))
            {
                methodSucceeded = true;
                Console.WriteLine("PageDown: TryMouseWheelPageScroll reported success");
            }
            else if (TryPageScrollMessage(hwnd, false))
            {
                methodSucceeded = true;
                Console.WriteLine("PageDown: TryPageScrollMessage reported success");
            }

            if (methodSucceeded)
            {
                // Wait for scroll to complete and verify position changed
                System.Threading.Thread.Sleep(200);
                int newPos = GetCurrentScrollPosition(hwnd);
                Console.WriteLine($"PageDown: New scroll position: {newPos}");

                bool actuallyScrolled = newPos != initialPos;
                Console.WriteLine($"PageDown: Actually scrolled: {actuallyScrolled}");
                return actuallyScrolled;
            }

            Console.WriteLine($"PageDown: All scroll methods failed.");
            return false;
        }
        
        private bool TryPageScrollMessage(IntPtr hwnd, bool pageUp)
        {
            try
            {
                SetForegroundWindow(hwnd);
                System.Threading.Thread.Sleep(50);
                
                IntPtr result = SendMessage(hwnd, WM_VSCROLL,
                    (IntPtr)(pageUp ? SB_PAGEUP : SB_PAGEDOWN), IntPtr.Zero);
                return result != IntPtr.Zero;
            }
            catch
            {
                return false;
            }
        }
        
        private bool TryMouseWheelPageScroll(IntPtr hwnd, bool pageUp)
        {
            try
            {
                // SetForegroundWindow(hwnd); // TryMouseWheelScroll will handle focus for each scroll
                // System.Threading.Thread.Sleep(50);

                bool allScrollsSucceeded = true;
                // Multiple wheel scrolls for page effect
                for (int i = 0; i < 5; i++)
                {
                    if (!TryMouseWheelScroll(hwnd, pageUp)) // TryMouseWheelScroll does its own focus and cursor
                    {
                        Console.WriteLine($"TryMouseWheelPageScroll: Single TryMouseWheelScroll attempt {i + 1} for pageUp={pageUp} failed.");
                        allScrollsSucceeded = false;
                        break; // If one scroll fails, the "page scroll" attempt is considered failed.
                    }
                    System.Threading.Thread.Sleep(30); // Delay between individual wheel scrolls
                }
                return allScrollsSucceeded;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TryMouseWheelPageScroll: Exception - {ex.Message}");
                return false;
            }
        }
        
        private bool TryKeyboardPageScroll(IntPtr hwnd, bool pageUp)
        {
            try
            {
                SetForegroundWindow(hwnd);
                System.Threading.Thread.Sleep(150); // Increased delay for focus

                // Click on the window to ensure it has focus on the content
                if (GetWindowRect(hwnd, out RECT rect))
                {
                    int centerX = (rect.Left + rect.Right) / 2;
                    int centerY = (rect.Top + rect.Bottom) / 2;

                    GetCursorPos(out POINT originalPos);
                    SetCursorPos(centerX, centerY);
                    System.Threading.Thread.Sleep(50); // Brief pause for cursor move

                    INPUT[] clickInputs = new INPUT[2];
                    // Mouse down
                    clickInputs[0] = new INPUT {
                        type = INPUT_MOUSE,
                        u = new InputUnion {
                            mi = new MOUSEINPUT {
                                dx = 0, dy = 0, mouseData = 0,
                                dwFlags = MOUSEEVENTF_LEFTDOWN, time = 0, dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };
                    // Mouse up
                    clickInputs[1] = new INPUT {
                        type = INPUT_MOUSE,
                        u = new InputUnion {
                            mi = new MOUSEINPUT {
                                dx = 0, dy = 0, mouseData = 0,
                                dwFlags = MOUSEEVENTF_LEFTUP, time = 0, dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };

                    SendInput(2, clickInputs, Marshal.SizeOf(typeof(INPUT)));
                    System.Threading.Thread.Sleep(150); // Pause after click for focus to take effect

                    SetCursorPos(originalPos.X, originalPos.Y);
                    System.Threading.Thread.Sleep(50); // Pause after restoring cursor
                }

                ushort vKey = pageUp ? VK_PRIOR : VK_SPACE; // VK_SPACE is generally good for browsers

                INPUT[] inputs = new INPUT[2];
                
                // Key down
                inputs[0].type = INPUT_KEYBOARD;
                inputs[0].u.ki.wVk = vKey;
                inputs[0].u.ki.dwFlags = 0;
                
                // Key up
                inputs[1].type = INPUT_KEYBOARD;
                inputs[1].u.ki.wVk = vKey;
                inputs[1].u.ki.dwFlags = KEYEVENTF_KEYUP;
                
                uint result = SendInput(2, inputs, Marshal.SizeOf(typeof(INPUT)));
                System.Threading.Thread.Sleep(100); // Delay after key presses
                return result == 2;
            }
            catch
            {
                return false;
            }
        }

        public async Task AutoScrollAndCaptureAsync(IntPtr hwnd, int scrollSteps, int delayMs, 
            Func<IntPtr, Task> captureCallback)
        {
            for (int i = 0; i < scrollSteps; i++)
            {
                await captureCallback(hwnd);
                await Task.Delay(delayMs);
                ScrollDown(hwnd);
                await Task.Delay(delayMs);
            }
        }

        public bool CanScroll(IntPtr hwnd)
        {
            if (hwnd == IntPtr.Zero)
            {
                Console.WriteLine("CanScroll: HWND is Zero.");
                return false;
            }

            // For modern applications, especially web browsers and complex UI frameworks,
            // the standard Windows scroll info may not be reliable. We'll use a more
            // permissive approach that assumes scrolling is possible unless we can
            // definitively prove otherwise.

            SCROLLINFO si = new SCROLLINFO();
            si.cbSize = (uint)Marshal.SizeOf(si);
            si.fMask = (uint)ScrollInfoMask.SIF_ALL;

            if (GetScrollInfo(hwnd, (int)ScrollBarDirection.SB_VERT, ref si))
            {
                Console.WriteLine($"CanScroll: GetScrollInfo - nPos: {si.nPos}, nMin: {si.nMin}, nMax: {si.nMax}, nPage: {si.nPage}");

                // If we have valid scroll info and it indicates we're at the bottom, respect that
                if (si.nMax > 0 && si.nPage > 0 && si.nPos >= (si.nMax - si.nPage))
                {
                    Console.WriteLine("CanScroll: At bottom according to scroll info");
                    return false;
                }

                // If scroll info indicates scrollable content, allow scrolling
                if (si.nMax > si.nPage && si.nMax > 0)
                {
                    Console.WriteLine("CanScroll: Scrollable content detected");
                    return true;
                }
            }

            // If GetScrollInfo failed or returned invalid data, assume scrolling is possible
            // This is more permissive for modern applications that may not use standard scrollbars
            Console.WriteLine("CanScroll: Assuming scrollable (fallback for modern apps)");
            return true;
        }

        public bool IsWindowMinimized(IntPtr hwnd)
        {
            if (hwnd == IntPtr.Zero) return false;
            return IsIconic(hwnd);
        }

        public bool RestoreWindow(IntPtr hwnd)
        {
            if (hwnd == IntPtr.Zero) return false;
            // ShowWindow with SW_RESTORE should un-minimize.
            // It might be necessary to call SetForegroundWindow after this if it's still obscured,
            // but for un-minimizing, this is the primary call.
            return ShowWindow(hwnd, SW_RESTORE);
        }
    }
}
