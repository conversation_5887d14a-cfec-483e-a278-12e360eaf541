<Window x:Class="CaptureMasterPro.ElementSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Select Capture Area"
        WindowStyle="None"
        ResizeMode="NoResize"
        AllowsTransparency="True"
        Background="Transparent"
        WindowState="Maximized"
        Topmost="True"
        ShowInTaskbar="False"
        Cursor="Cross">
    
    <Grid Background="Transparent" IsHitTestVisible="False">
        <!-- Semi-transparent overlay - make it non-interactive -->
        <Rectangle Fill="#20000000" IsHitTestVisible="False" />

        <!-- Selection frame (initially hidden) -->
        <Border x:Name="selectionFrame"
                BorderThickness="3"
                BorderBrush="#FF00FF00"
                Background="Transparent"
                Visibility="Hidden"
                IsHitTestVisible="False">
            <Border.Effect>
                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.7"/>
            </Border.Effect>
        </Border>
        
        <!-- Instructions panel - make it interactive -->
        <Border Background="#E0FFFFFF"
                CornerRadius="8"
                Padding="15"
                HorizontalAlignment="Center"
                VerticalAlignment="Top"
                Margin="0,50,0,0"
                IsHitTestVisible="True">
            <StackPanel>
                <TextBlock Text="🎯 Smart Element Selection" 
                          FontSize="16" 
                          FontWeight="Bold" 
                          HorizontalAlignment="Center"
                          Margin="0,0,0,10"/>
                
                <TextBlock TextWrapping="Wrap" MaxWidth="400">
                    <Run Text="Move your mouse over the target window to select capture area:"/>
                    <LineBreak/>
                    <LineBreak/>
                    <Run Text="🟢 Green Frame" FontWeight="Bold" Foreground="Green"/> 
                    <Run Text=" = Content Area Only (recommended for scroll capture)"/>
                    <LineBreak/>
                    <Run Text="🔴 Red Frame" FontWeight="Bold" Foreground="Red"/> 
                    <Run Text=" = Full Window (includes toolbars, borders)"/>
                    <LineBreak/>
                    <Run Text="🔵 Blue Frame" FontWeight="Bold" Foreground="Blue"/> 
                    <Run Text=" = Custom Area (specific UI elements)"/>
                    <LineBreak/>
                    <LineBreak/>
                    <Run Text="Click to select the highlighted area, or press ESC to cancel."/>
                </TextBlock>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                    <TextBlock x:Name="txtCurrentSelection" 
                              Text="Move mouse to detect elements..."
                              FontStyle="Italic"
                              Foreground="Gray"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>
