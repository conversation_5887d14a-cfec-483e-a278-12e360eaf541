move all the files of the project to C:\AI-Programmi\mp-sit\ that is the base folder, don't use other folders. to use less space possible, add fnction to load images, videos, music, hosted on different websites that appears on the MyPage. the website host has to save only the website structure and text of messages, and media saved on other webisites that free host, as rumble.com for hosting video, but managed and watched from MyPage, and other sites for images, etc. create a dashboard with modern style and effect for the admin and keep track of everything, all the users info, messages, private chat, passwords, and anything that can be saved.



the folder has to be only C:\AI-Programmi\e-com-s for the site, not other. so remove other files in other folders or move the site files in C:\AI-Programmi\e-com-s. 



