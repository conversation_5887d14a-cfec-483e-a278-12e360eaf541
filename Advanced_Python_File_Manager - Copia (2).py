import sys
import os
import shutil
import filecmp
import difflib
from datetime import datetime
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QPushButton, QTableWidget, QTableWidgetItem,
    QLabel, QTextEdit, QFileDialog, QMessageBox, QFrame,
    QStackedLayout, QHeaderView, QAbstractItemView, QProgressBar
)
from PyQt6.QtGui import QFont, QDragEnterEvent, QDropEvent, QPalette, QColor, QPainter, QPen, QBrush
from PyQt6.QtCore import Qt, QThread, QObject, pyqtSignal, QRect, QTimer

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  UI COLORS & WORKERS
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
COLOR_UNIQUE = QColor(255, 0, 0, 40)
COLOR_PRESENT_IN_BOTH = QColor(0, 128, 0, 40)

class BatchComparisonWorker(QObject):
    progress = pyqtSignal(int, int)
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, path1, path2, common_files):
        super().__init__()
        self.path1 = path1
        self.path2 = path2
        self.common_files = common_files
        self._is_running = True

    def run(self):
        try:
            diff_files = []
            total = len(self.common_files)
            for i, filename in enumerate(self.common_files):
                if not self._is_running: break
                path_a = os.path.join(self.path1, filename)
                path_b = os.path.join(self.path2, filename)
                if not filecmp.cmp(path_a, path_b, shallow=False):
                    diff_files.append(filename)
                if i % 10 == 0 or i == total - 1:
                    self.progress.emit(i + 1, total)
            if self._is_running:
                self.finished.emit(diff_files)
        except Exception as e:
            self.error.emit(f"Comparison failed: {e}")

    def stop(self):
        self._is_running = False

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  ANIMATED LOADING OVERLAY with Progress Bar
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class LoadingOverlay(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setVisible(False)
        self.angle = 0
        self.timer = QTimer(self, interval=20)
        self.timer.timeout.connect(self.update_animation)

        layout = QVBoxLayout(self)
        layout.addStretch()
        self.label = QLabel("Loading...")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label.setStyleSheet("color: white; font-size: 14px; font-weight: bold;")
        layout.addWidget(self.label)
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar { border: 2px solid grey; border-radius: 5px; text-align: center; background-color: #2a2a2a; color: white; }
            QProgressBar::chunk { background-color: #05B8CC; width: 10px; margin: 0.5px; }
        """)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        layout.addStretch()

    def paintEvent(self, event):
        painter = QPainter(self)
        if not painter.isActive(): return
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(QBrush(QColor(0, 0, 0, 180)))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(self.rect())

    def update_animation(self):
        self.angle = (self.angle + 10) % 360
        self.update()

    def start_animation(self, text="Discovering..."):
        self.label.setText(text)
        self.progress_bar.setVisible(False)
        self.resize(self.parent().size())
        self.setVisible(True)
        self.timer.start()

    def stop_animation(self):
        self.timer.stop()
        self.setVisible(False)

    def set_progress(self, current, total):
        self.label.setText(f"Comparing... {int((current/total)*100)}%")
        if not self.progress_bar.isVisible():
            self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  CustomTableWidgetItem & REPORT WINDOW
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class CustomTableWidgetItem(QTableWidgetItem):
    def __lt__(self, other):
        data_self = self.data(Qt.ItemDataRole.UserRole)
        data_other = other.data(Qt.ItemDataRole.UserRole)
        if data_self is not None and data_other is not None and isinstance(data_self, (int, float, datetime)):
            return data_self < data_other
        return super().__lt__(other)

class ReportWindow(QWidget):
    def __init__(self, title, report_text):
        super().__init__()
        self.report_text = report_text
        self.setWindowTitle(title)
        self.setGeometry(250, 250, 800, 600)
        layout = QVBoxLayout(self)
        self.text_edit = QTextEdit()
        self.text_edit.setFont(QFont("Consolas", 10))
        self.text_edit.setPlainText(self.report_text)
        self.text_edit.setReadOnly(True)
        layout.addWidget(self.text_edit)
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("Save Report")
        self.save_button.clicked.connect(self.save_report)
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        layout.addLayout(button_layout)

    def save_report(self):
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Report", "comparison_report.txt", "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f: f.write(self.report_text)
                QMessageBox.information(self, "Success", "Report saved successfully.")
            except Exception as e: QMessageBox.critical(self, "Error", f"Could not save report:\n{e}")

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  FILE PANEL WIDGET
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class FilePanel(QFrame):
    selection_changed = pyqtSignal()
    
    def __init__(self, side_name, main_window):
        super().__init__()
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.side_name = side_name
        self.main_window = main_window
        self.content_type = None
        self.folder_path = None
        self.file_path = None
        self.thread = None
        self.worker = None
        self.is_busy = False
        panel_layout = QVBoxLayout(self)
        button_bar_layout = QHBoxLayout()
        panel_layout.addLayout(button_bar_layout)
        filter_bar_layout = QHBoxLayout()
        panel_layout.addLayout(filter_bar_layout)
        self.path_label = QLabel("No content loaded.")
        self.path_label.setStyleSheet("padding: 4px; background-color: #2a2a2a; border-radius: 4px; font-style: italic;")
        self.path_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        panel_layout.addWidget(self.path_label)
        self.stacked_layout = QStackedLayout()
        self.table_widget = self._create_table_widget()
        self.text_viewer = self._create_text_viewer()
        self.add_prompt_button = self._create_prompt_button()
        self.stacked_layout.addWidget(self.table_widget)
        self.stacked_layout.addWidget(self.text_viewer)
        self.stacked_layout.addWidget(self.add_prompt_button)
        panel_layout.addLayout(self.stacked_layout)
        self.stacked_layout.setCurrentWidget(self.add_prompt_button)
        self.cancel_button = QPushButton("Clear Panel")
        self.cancel_button.clicked.connect(self.clear_panel)
        self.cancel_button.setVisible(False)
        panel_layout.addWidget(self.cancel_button)
        self._create_buttons(button_bar_layout, filter_bar_layout)
        self.setAcceptDrops(True)
        self.loading_overlay = LoadingOverlay(self)

    def resizeEvent(self, event):
        self.loading_overlay.resize(event.size())
        super().resizeEvent(event)
    
    def clear_panel(self):
        self.cleanup_thread()
        self.content_type = None
        self.folder_path = None
        self.file_path = None
        self.path_label.setText("No content loaded.")
        self.stacked_layout.setCurrentWidget(self.add_prompt_button)
        self.cancel_button.setVisible(False)
        self.main_window.on_panel_cleared()

    def _create_table_widget(self):
        table = QTableWidget()
        table.setColumnCount(4)
        headers = ["Name", "Last Modified", "Type", "Size"]
        table.setHorizontalHeaderLabels(headers)
        table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        table.verticalHeader().setVisible(False)
        table.horizontalHeader().setSortIndicatorShown(True)
        table.horizontalHeader().sectionClicked.connect(self.sort_by_column)
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        table.setSortingEnabled(True)
        table.itemSelectionChanged.connect(self.selection_changed.emit)
        return table

    def _create_text_viewer(self):
        viewer = QTextEdit()
        viewer.setReadOnly(True)
        viewer.setFont(QFont("Consolas", 10))
        return viewer

    def _create_prompt_button(self):
        button = QPushButton(f"+\n\nDrag & Drop Folder or Text File")
        button.setFont(QFont("Segoe UI", 18))
        button.setStyleSheet("color: #6a717b; border: 2px dashed #6a717b;")
        button.clicked.connect(self.add_folder)
        return button

    def _create_buttons(self, button_bar, filter_bar):
        self.btn_add = QPushButton("Add Folder")
        self.btn_add.clicked.connect(self.add_folder)
        button_bar.addWidget(self.btn_add)
        self.btn_save = QPushButton("Save List")
        self.btn_save.clicked.connect(self.save_file_list)
        button_bar.addWidget(self.btn_save)
        filter_label = QLabel("Filters:")
        filter_label.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
        filter_bar.addWidget(filter_label)
        self.btn_all = QPushButton("All Files")
        self.btn_all.clicked.connect(lambda: QMessageBox.information(self, "Notice", "Filtering is disabled to ensure comparison stability."))
        filter_bar.addWidget(self.btn_all)
        filter_bar.addStretch()

    def load_folder(self, folder_path):
        if not os.path.isdir(folder_path) or self.is_busy: return
        self.is_busy = True
        self.main_window.update_button_states()
        self.loading_overlay.start_animation("Loading...")
        self.cleanup_thread()
        self.folder_path = folder_path
        self.content_type = 'folder'
        self.file_path = None
        self.path_label.setText(folder_path)
        try:
            items = []
            for item_name in os.listdir(self.folder_path):
                full_path = os.path.join(self.folder_path, item_name)
                stats = os.stat(full_path)
                is_dir = os.path.isdir(full_path)
                ext = os.path.splitext(item_name)[1].lower() if not is_dir else ''
                items.append((item_name, stats, ext, is_dir))
            self.on_loading_finished(items)
        except Exception as e:
            self.on_loading_error(f"Could not load content: {e}")

    def on_loading_finished(self, results):
        self.clear_highlighting()
        self.table_widget.setSortingEnabled(False)
        self.table_widget.setRowCount(0)
        for name, stats, ext, is_dir in results:
            self.add_file_to_table(name, stats, ext, is_dir)
        self.table_widget.setSortingEnabled(True)
        self.stacked_layout.setCurrentWidget(self.table_widget)
        self.cancel_button.setVisible(True)
        self.is_busy = False
        self.loading_overlay.stop_animation()
        self.main_window.update_button_states()

    def on_loading_error(self, err_msg):
        QMessageBox.critical(self, "Error", err_msg)
        self.is_busy = False
        self.loading_overlay.stop_animation()
        self.main_window.update_button_states()
    
    def cleanup_thread(self):
        if self.thread and self.thread.isRunning():
            if self.worker: self.worker.stop()
            self.thread.quit()
            self.thread.wait()

    def load_text_file(self, file_path):
        if self.is_busy: return
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f: content = f.read()
            self.text_viewer.setPlainText(content)
            self.content_type = 'file'
            self.folder_path = None
            self.file_path = file_path
            self.path_label.setText(file_path)
            self.stacked_layout.setCurrentWidget(self.text_viewer)
            self.cancel_button.setVisible(True)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Could not read file:\n{e}")
            self.clear_panel()
        self.main_window.update_button_states()

    def add_file_to_table(self, name, stats, ext, is_dir):
        mod_time = datetime.fromtimestamp(stats.st_mtime)
        size_bytes = stats.st_size
        name_item = CustomTableWidgetItem(name)
        mod_time_item = CustomTableWidgetItem(mod_time.strftime('%Y-%m-%d %H:%M'))
        type_item = CustomTableWidgetItem("Folder" if is_dir else f"{ext.upper()} File")
        size_item = CustomTableWidgetItem("" if is_dir else self.format_size(size_bytes))
        name_item.setData(Qt.ItemDataRole.UserRole, name)
        mod_time_item.setData(Qt.ItemDataRole.UserRole, mod_time)
        type_item.setData(Qt.ItemDataRole.UserRole, type_item.text())
        size_item.setData(Qt.ItemDataRole.UserRole, size_bytes if not is_dir else -1)
        row_position = self.table_widget.rowCount()
        self.table_widget.insertRow(row_position)
        self.table_widget.setItem(row_position, 0, name_item)
        self.table_widget.setItem(row_position, 1, mod_time_item)
        self.table_widget.setItem(row_position, 2, type_item)
        self.table_widget.setItem(row_position, 3, size_item)

    def format_size(self, size_bytes):
        if size_bytes == 0: return "0 B"
        size_names = ("B", "KB", "MB", "GB", "TB")
        i = int(size_bytes).bit_length() // 10
        power = 1024 ** i
        return f"{round(size_bytes / power, 2)} {size_names[i]}"

    def sort_by_column(self, col_index):
        self.table_widget.sortItems(col_index, self.table_widget.horizontalHeader().sortIndicatorOrder())
    
    # --- THE FIX: This method was accidentally deleted and has been restored ---
    def add_folder(self):
        if self.is_busy: return
        folder_path = QFileDialog.getExistingDirectory(self, "Select a Folder")
        if folder_path:
            self.load_folder(folder_path)

    def save_file_list(self):
        if self.content_type != 'folder' or self.table_widget.rowCount() == 0: return
        current_folder_path = self.folder_path
        current_folder_name = os.path.basename(os.path.normpath(current_folder_path))
        parent_folder_path = os.path.dirname(current_folder_path)
        parent_folder_name = os.path.basename(parent_folder_path)
        sanitized_current = current_folder_name.replace(' ', '_')
        sanitized_parent = parent_folder_name.replace(' ', '_')
        default_name = f"{sanitized_parent}-{sanitized_current}-list.txt" if sanitized_parent else f"{sanitized_current}-list.txt"
        try:
            script_dir = os.path.dirname(os.path.realpath(__file__))
            save_dir = os.path.join(script_dir, "save_list")
            os.makedirs(save_dir, exist_ok=True)
        except Exception: save_dir = ""
        full_default_path = os.path.join(save_dir, default_name)
        file_path, _ = QFileDialog.getSaveFileName(self, "Save File List", full_default_path, "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    headers = [self.table_widget.horizontalHeaderItem(i).text() for i in range(self.table_widget.columnCount())]
                    f.write("\t".join(headers) + "\n")
                    for row in range(self.table_widget.rowCount()):
                        row_data = [self.table_widget.item(row, col).text() for col in range(self.table_widget.columnCount())]
                        f.write("\t".join(row_data) + "\n")
                QMessageBox.information(self, "Success", "File list saved successfully.")
            except Exception as e: QMessageBox.critical(self, "Error", f"Could not save file:\n{e}")

    def get_selected_filenames(self):
        selected_rows = sorted(list(set(index.row() for index in self.table_widget.selectedIndexes())))
        return [self.table_widget.item(row, 0).text() for row in selected_rows]

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls(): event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        if self.is_busy: return
        for url in event.mimeData().urls():
            if url.isLocalFile():
                path = url.toLocalFile()
                if os.path.isdir(path): self.load_folder(path); break
                elif os.path.isfile(path) and path.lower().endswith('.txt'): self.load_text_file(path); break

    def apply_comparison_highlighting(self, unique_files, present_in_both):
        self.clear_highlighting()
        for row in range(self.table_widget.rowCount()):
            filename = self.table_widget.item(row, 0).text()
            color = None
            if filename in unique_files: color = COLOR_UNIQUE
            elif filename in present_in_both: color = COLOR_PRESENT_IN_BOTH
            if color:
                for col in range(self.table_widget.columnCount()):
                    self.table_widget.item(row, col).setBackground(QBrush(color))
    
    def clear_highlighting(self):
        for row in range(self.table_widget.rowCount()):
            for col in range(self.table_widget.columnCount()):
                if self.table_widget.item(row, col): self.table_widget.item(row, col).setBackground(QBrush(Qt.GlobalColor.transparent))

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  MAIN WINDOW
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class ModernFileManager(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Advanced Python File Manager")
        self.setGeometry(50, 50, 1900, 1000)
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)
        self.left_panel = FilePanel("Left", self)
        self.right_panel = FilePanel("Right", self)
        self.last_folder_comparison_results = None
        self.comparison_thread = None
        self.comparison_worker = None
        main_layout.addWidget(self.left_panel, 5)
        self.create_center_panel(main_layout)
        main_layout.addWidget(self.right_panel, 5)
        self.left_panel.selection_changed.connect(self.update_button_states)
        self.right_panel.selection_changed.connect(self.update_button_states)
        self.update_button_states()

    def create_center_panel(self, layout):
        center_frame = QFrame()
        center_layout = QVBoxLayout(center_frame)
        center_layout.addStretch()
        self.btn_compare_folders = QPushButton("Compare\nFolders")
        self.btn_compare_folders.clicked.connect(self.compare_folders)
        center_layout.addWidget(self.btn_compare_folders)
        self.btn_move_selected_to_right = QPushButton("Move Selected ->")
        self.btn_move_selected_to_right.clicked.connect(lambda: self.move_selected('left_to_right'))
        self.btn_move_selected_to_right.setVisible(False)
        center_layout.addWidget(self.btn_move_selected_to_right)
        self.btn_move_selected_to_left = QPushButton("<- Move Selected")
        self.btn_move_selected_to_left.clicked.connect(lambda: self.move_selected('right_to_left'))
        self.btn_move_selected_to_left.setVisible(False)
        center_layout.addWidget(self.btn_move_selected_to_left)
        self.btn_move_missing_to_right = QPushButton("Move Missing ->")
        self.btn_move_missing_to_right.clicked.connect(lambda: self.move_missing('left_to_right'))
        self.btn_move_missing_to_right.setVisible(False)
        center_layout.addWidget(self.btn_move_missing_to_right)
        self.btn_move_missing_to_left = QPushButton("<- Move Missing")
        self.btn_move_missing_to_left.clicked.connect(lambda: self.move_missing('right_to_left'))
        self.btn_move_missing_to_left.setVisible(False)
        center_layout.addWidget(self.btn_move_missing_to_left)
        self.btn_compare_text = QPushButton("Compare\nText Files")
        self.btn_compare_text.clicked.connect(self.compare_text_files)
        center_layout.addWidget(self.btn_compare_text)
        center_layout.addStretch()
        layout.addWidget(center_frame, 1)

    def on_panel_cleared(self):
        self.last_folder_comparison_results = None
        self.left_panel.clear_highlighting()
        self.right_panel.clear_highlighting()
        self.update_button_states()

    def update_button_states(self):
        is_folder_mode_both = self.left_panel.content_type == 'folder' and self.right_panel.content_type == 'folder'
        is_file_mode_both = self.left_panel.content_type == 'file' and self.right_panel.content_type == 'file'
        
        self.btn_compare_folders.setEnabled(is_folder_mode_both and not self.left_panel.is_busy and not self.right_panel.is_busy)
        self.btn_compare_text.setEnabled(is_file_mode_both)
        
        comparison_active = self.last_folder_comparison_results is not None
        self.btn_move_missing_to_right.setVisible(comparison_active)
        self.btn_move_missing_to_left.setVisible(comparison_active)
        
        left_has_selection = is_folder_mode_both and len(self.left_panel.get_selected_filenames()) > 0
        right_has_selection = is_folder_mode_both and len(self.right_panel.get_selected_filenames()) > 0
        self.btn_move_selected_to_right.setVisible(is_folder_mode_both)
        self.btn_move_selected_to_left.setVisible(is_folder_mode_both)
        self.btn_move_selected_to_right.setEnabled(left_has_selection)
        self.btn_move_selected_to_left.setEnabled(right_has_selection)
        
        for panel in [self.left_panel, self.right_panel]:
            is_folder = panel.content_type == 'folder'
            panel.btn_save.setEnabled(is_folder)

    def compare_folders(self):
        if self.left_panel.is_busy or self.right_panel.is_busy: return
        self.on_panel_cleared()
        self.left_panel.loading_overlay.start_animation("Discovering...")
        self.right_panel.loading_overlay.start_animation("Discovering...")
        QApplication.processEvents()
        
        try:
            left_files = {f for f in os.listdir(self.left_panel.folder_path) if os.path.isfile(os.path.join(self.left_panel.folder_path, f))}
            right_files = {f for f in os.listdir(self.right_panel.folder_path) if os.path.isfile(os.path.join(self.right_panel.folder_path, f))}
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Could not list files for discovery: {e}")
            self.left_panel.loading_overlay.stop_animation()
            self.right_panel.loading_overlay.stop_animation()
            return
            
        common_files = list(left_files & right_files)
        self.last_folder_comparison_results = {
            'left_only': list(left_files - right_files),
            'right_only': list(right_files - right_files)
        }
        
        if not common_files:
            self.on_folder_comparison_finished([])
            return

        self.left_panel.loading_overlay.set_progress(0, len(common_files))
        self.right_panel.loading_overlay.set_progress(0, len(common_files))

        self.comparison_thread = QThread()
        self.comparison_worker = BatchComparisonWorker(self.left_panel.folder_path, self.right_panel.folder_path, common_files)
        self.comparison_worker.moveToThread(self.comparison_thread)
        self.comparison_thread.started.connect(self.comparison_worker.run)
        self.comparison_worker.progress.connect(self.on_comparison_progress)
        self.comparison_worker.finished.connect(self.on_folder_comparison_finished)
        self.comparison_worker.error.connect(self.on_comparison_error)
        self.comparison_thread.start()

    def on_comparison_progress(self, current, total):
        self.left_panel.loading_overlay.set_progress(current, total)
        self.right_panel.loading_overlay.set_progress(current, total)

    def on_folder_comparison_finished(self, diff_files):
        self.left_panel.loading_overlay.stop_animation()
        self.right_panel.loading_overlay.stop_animation()
        
        common_files_set = set(os.listdir(self.left_panel.folder_path)) & set(os.listdir(self.right_panel.folder_path))
        
        self.left_panel.apply_comparison_highlighting(self.last_folder_comparison_results['left_only'], common_files_set)
        self.right_panel.apply_comparison_highlighting(self.last_folder_comparison_results['right_only'], common_files_set)
        
        self.update_button_states()
        if self.comparison_thread: self.comparison_thread.quit()

    def compare_text_files(self):
        if self.left_panel.is_busy or self.right_panel.is_busy: return
        self.left_panel.loading_overlay.start_animation("Comparing...")
        self.right_panel.loading_overlay.start_animation("Comparing...")
        QApplication.processEvents()
        
        file1, file2 = self.left_panel.file_path, self.right_panel.file_path
        try:
            with open(file1, 'r', errors='ignore') as f1, open(file2, 'r', errors='ignore') as f2:
                diff = difflib.unified_diff(f1.readlines(), f2.readlines(), fromfile=os.path.basename(file1), tofile=os.path.basename(file2))
                report = "".join(diff)
                self.show_report_window("Text Comparison Report", report if report else "The selected files are identical.")
        except Exception as e:
            self.on_comparison_error(f"Text comparison failed: {e}")

        self.left_panel.loading_overlay.stop_animation()
        self.right_panel.loading_overlay.stop_animation()

    def on_comparison_error(self, err_msg):
        self.left_panel.loading_overlay.stop_animation()
        self.right_panel.loading_overlay.stop_animation()
        QMessageBox.critical(self, "Error", err_msg)
        self.update_button_states()
        
    def move_selected(self, direction):
        if direction == 'left_to_right': source_panel, dest_panel = self.left_panel, self.right_panel
        else: source_panel, dest_panel = self.right_panel, self.left_panel
        files_to_move = source_panel.get_selected_filenames()
        if not files_to_move: return
        for filename in files_to_move:
            source_path = os.path.join(source_panel.folder_path, filename)
            dest_path = os.path.join(dest_panel.folder_path, filename)
            try: shutil.move(source_path, dest_path)
            except Exception as e: QMessageBox.critical(self, "Error", f"Could not move {filename}:\n{e}"); break
        source_panel.load_folder(source_panel.folder_path)
        dest_panel.load_folder(dest_panel.folder_path)
        self.on_panel_cleared()
        
    def move_missing(self, direction):
        if not self.last_folder_comparison_results: return
        if direction == 'left_to_right':
            source_panel, dest_panel = self.left_panel, self.right_panel
            files_to_move = self.last_folder_comparison_results['left_only']
        else:
            source_panel, dest_panel = self.right_panel, self.left_panel
            files_to_move = self.last_folder_comparison_results['right_only']
        if not files_to_move:
            QMessageBox.information(self, "No Files to Move", "There are no unique files to move from the source.")
            return
        for filename in files_to_move:
            source_path = os.path.join(source_panel.folder_path, filename)
            dest_path = os.path.join(dest_panel.folder_path, filename)
            try: shutil.copy2(source_path, dest_path)
            except Exception as e: QMessageBox.critical(self, "Error", f"Could not copy {filename}:\n{e}"); break
        dest_panel.load_folder(dest_panel.folder_path)
        self.compare_folders()

    def show_report_window(self, title, report_text):
        self.report_win = ReportWindow(title, report_text)
        self.report_win.show()

def setup_dark_theme(app):
    app.setStyle("Fusion")
    dark_palette = QPalette()
    dark_palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
    dark_palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.white)
    dark_palette.setColor(QPalette.ColorRole.Base, QColor(35, 35, 35))
    dark_palette.setColor(QPalette.ColorRole.AlternateBase, QColor(45, 45, 45))
    dark_palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(53, 53, 53))
    dark_palette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.white)
    dark_palette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.white)
    dark_palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
    dark_palette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.white)
    dark_palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.red)
    dark_palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
    dark_palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
    dark_palette.setColor(QPalette.ColorRole.HighlightedText, QColor(25, 25, 25))
    app.setPalette(dark_palette)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    setup_dark_theme(app)
    window = ModernFileManager()
    window.show()
    sys.exit(app.exec())