<Window x:Class="CaptureMasterPro.ScrollCaptureOverlayWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Scroll Capture Controls"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        ShowInTaskbar="False" Topmost="True" ResizeMode="NoResize"
        Loaded="ScrollCaptureOverlayWindow_Loaded">
    <Window.Resources>
        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#4A90E2" Offset="0"/>
            <GradientStop Color="#357ABD" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SuccessGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#5CB85C" Offset="0"/>
            <GradientStop Color="#449D44" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="DangerGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#D9534F" Offset="0"/>
            <GradientStop Color="#C9302C" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#6C757D" Offset="0"/>
            <GradientStop Color="#5A6268" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryGradient}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="Margin" Value="8,4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="IsTabStop" Value="False"/>
            <Setter Property="Focusable" Value="False"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="Height" Value="44"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                x:Name="ButtonBorder">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.3" ShadowDepth="2" BlurRadius="4"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            TextElement.FontFamily="{TemplateBinding FontFamily}"
                                            TextElement.FontSize="{TemplateBinding FontSize}"
                                            TextElement.FontWeight="{TemplateBinding FontWeight}"
                                            TextElement.Foreground="{TemplateBinding Foreground}"
                                            IsHitTestVisible="False"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Opacity="0.5" ShadowDepth="3" BlurRadius="6"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="ButtonBorder" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Opacity="0.2" ShadowDepth="1" BlurRadius="2"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessGradient}"/>
        </Style>

        <!-- Danger Button Style -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource DangerGradient}"/>
        </Style>

        <!-- Secondary Button Style -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SecondaryGradient}"/>
        </Style>

        <!-- Compact Button Styles -->
        <Style x:Key="CompactButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryGradient}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="28"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF5BA0F2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF2E7BD6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CompactSuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource CompactButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessGradient}"/>
        </Style>

        <Style x:Key="CompactDangerButtonStyle" TargetType="Button" BasedOn="{StaticResource CompactButtonStyle}">
            <Setter Property="Background" Value="{StaticResource DangerGradient}"/>
        </Style>
    </Window.Resources>
    <!-- Compact Control Panel - Fixed at top -->
    <Border x:Name="SelectionBorder"
            BorderBrush="#FF4A90E2"
            BorderThickness="2"
            CornerRadius="8"
            Background="#F0000000"
            VerticalAlignment="Top"
            HorizontalAlignment="Center"
            Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.6" ShadowDepth="3" BlurRadius="6"/>
        </Border.Effect>

        <StackPanel Orientation="Horizontal" Margin="12,8">
            <!-- Compact Status -->
            <TextBlock x:Name="txtStatus"
                      Text="Ready"
                      Foreground="White"
                      FontSize="11"
                      FontWeight="Medium"
                      VerticalAlignment="Center"
                      Margin="0,0,12,0"/>

            <TextBlock x:Name="txtCaptureCount"
                      Text="0"
                      Foreground="#FFB0B0B0"
                      FontSize="10"
                      VerticalAlignment="Center"
                      Margin="0,0,12,0"/>

            <!-- Compact Buttons -->
            <Button x:Name="btnScrollUp"
                   Click="btnScrollUp_Click"
                   Style="{StaticResource CompactButtonStyle}"
                   ToolTip="Scroll up one page"
                   Content="↑"/>

            <Button x:Name="btnScrollDown"
                   Click="btnScrollDown_Click"
                   Style="{StaticResource CompactButtonStyle}"
                   ToolTip="Scroll down one page"
                   Content="↓"/>

            <Button x:Name="btnSave"
                   Click="btnSave_Click"
                   Style="{StaticResource CompactSuccessButtonStyle}"
                   ToolTip="Save and compose all captured screenshots"
                   Content="💾"/>

            <Button x:Name="btnCancel"
                   Click="btnCancel_Click"
                   Style="{StaticResource CompactDangerButtonStyle}"
                   ToolTip="Cancel scroll capture"
                   Content="✖"/>
        </StackPanel>
    </Border>
</Window>