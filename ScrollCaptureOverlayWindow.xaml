<Window x:Class="CaptureMasterPro.ScrollCaptureOverlayWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Scroll Capture Controls"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        ShowInTaskbar="False" Topmost="True" ResizeMode="NoResize"
        Loaded="ScrollCaptureOverlayWindow_Loaded">
    <Window.Resources>
        <!-- Overlay Button Style -->
        <Style x:Key="OverlayButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="IsTabStop" Value="False"/>
            <Setter Property="Focusable" Value="False"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            TextElement.FontFamily="{TemplateBinding FontFamily}"
                                            TextElement.FontSize="{TemplateBinding FontSize}"
                                            TextElement.FontWeight="{TemplateBinding FontWeight}"
                                            TextElement.Foreground="{TemplateBinding Foreground}"
                                            IsHitTestVisible="False"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F0F0F0"/>
                                <Setter Property="BorderBrush" Value="#999999"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E0E0E0"/>
                                <Setter Property="BorderBrush" Value="#666666"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <Border BorderBrush="Red" BorderThickness="3" CornerRadius="5" Background="#22FFFFFF" x:Name="SelectionBorder">
        <Grid>
            <StackPanel x:Name="ButtonPanel" Orientation="Horizontal" VerticalAlignment="Top" HorizontalAlignment="Center" Margin="10">
                <Button x:Name="btnScrollUp" Content="↑ Scroll Up" Click="btnScrollUp_Click" Style="{StaticResource OverlayButtonStyle}"/>
                <Button x:Name="btnScrollDown" Content="↓ Scroll Down" Click="btnScrollDown_Click" Style="{StaticResource OverlayButtonStyle}"/>
                <Button x:Name="btnSave" Content="✔ Save" Click="btnSave_Click" Style="{StaticResource OverlayButtonStyle}" Background="LightGreen"/>
                <Button x:Name="btnCancel" Content="✖ Cancel" Click="btnCancel_Click" Style="{StaticResource OverlayButtonStyle}" Background="LightCoral"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>