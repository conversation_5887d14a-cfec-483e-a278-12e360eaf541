<Window x:Class="CaptureMasterPro.ScrollCaptureOverlayWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Scroll Capture Controls"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        ShowInTaskbar="False" Topmost="True" ResizeMode="NoResize"
        Loaded="ScrollCaptureOverlayWindow_Loaded">
    <Window.Resources>
        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#4A90E2" Offset="0"/>
            <GradientStop Color="#357ABD" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SuccessGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#5CB85C" Offset="0"/>
            <GradientStop Color="#449D44" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="DangerGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#D9534F" Offset="0"/>
            <GradientStop Color="#C9302C" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#6C757D" Offset="0"/>
            <GradientStop Color="#5A6268" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryGradient}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="Margin" Value="8,4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="IsTabStop" Value="False"/>
            <Setter Property="Focusable" Value="False"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="Height" Value="44"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                x:Name="ButtonBorder">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.3" ShadowDepth="2" BlurRadius="4"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            TextElement.FontFamily="{TemplateBinding FontFamily}"
                                            TextElement.FontSize="{TemplateBinding FontSize}"
                                            TextElement.FontWeight="{TemplateBinding FontWeight}"
                                            TextElement.Foreground="{TemplateBinding Foreground}"
                                            IsHitTestVisible="False"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Opacity="0.5" ShadowDepth="3" BlurRadius="6"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="ButtonBorder" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Opacity="0.2" ShadowDepth="1" BlurRadius="2"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessGradient}"/>
        </Style>

        <!-- Danger Button Style -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource DangerGradient}"/>
        </Style>

        <!-- Secondary Button Style -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SecondaryGradient}"/>
        </Style>

        <!-- Status Text Style -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>
    </Window.Resources>
    <!-- Modern Control Panel -->
    <Border x:Name="SelectionBorder"
            BorderBrush="#FF4A90E2"
            BorderThickness="3"
            CornerRadius="12"
            Background="#E6000000"
            Margin="20">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.4" ShadowDepth="4" BlurRadius="8"/>
        </Border.Effect>

        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                <TextBlock Text="📸" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBlock Text="Scroll Screenshot Capture"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="White"
                          VerticalAlignment="Center"/>
            </StackPanel>

            <!-- Status Display -->
            <Border Grid.Row="1"
                    Background="#33FFFFFF"
                    CornerRadius="6"
                    Padding="12,8"
                    Margin="0,0,0,15"
                    x:Name="StatusPanel">
                <StackPanel>
                    <TextBlock x:Name="txtStatus"
                              Text="Ready to capture. Use scroll buttons to navigate through content."
                              Style="{StaticResource StatusTextStyle}"/>
                    <TextBlock x:Name="txtCaptureCount"
                              Text="Captures: 0"
                              Style="{StaticResource StatusTextStyle}"
                              FontSize="11"
                              Opacity="0.8"/>
                </StackPanel>
            </Border>

            <!-- Main Control Buttons -->
            <StackPanel Grid.Row="2"
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,15">
                <Button x:Name="btnScrollUp"
                       Click="btnScrollUp_Click"
                       Style="{StaticResource SecondaryButtonStyle}"
                       ToolTip="Scroll up one page">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⬆" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="Scroll Up"/>
                    </StackPanel>
                </Button>

                <Button x:Name="btnScrollDown"
                       Click="btnScrollDown_Click"
                       Style="{StaticResource ModernButtonStyle}"
                       ToolTip="Scroll down one page">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⬇" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="Scroll Down"/>
                    </StackPanel>
                </Button>
            </StackPanel>

            <!-- Action Buttons -->
            <StackPanel Grid.Row="3"
                       Orientation="Horizontal"
                       HorizontalAlignment="Center">
                <Button x:Name="btnSave"
                       Click="btnSave_Click"
                       Style="{StaticResource SuccessButtonStyle}"
                       ToolTip="Save and compose all captured screenshots">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="Save &amp; Compose"/>
                    </StackPanel>
                </Button>

                <Button x:Name="btnCancel"
                       Click="btnCancel_Click"
                       Style="{StaticResource DangerButtonStyle}"
                       ToolTip="Cancel scroll capture">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="Cancel"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>