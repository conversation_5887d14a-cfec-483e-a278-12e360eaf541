import sys
import os
import shutil
import filecmp
import difflib
import math
import json
import hashlib
from datetime import datetime
from functools import partial
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QPushButton, QTableWidget, QTableWidgetItem,
    QLabel, QTextEdit, QFileDialog, QMessageBox, QFrame,
    QStackedLayout, QHeaderView, QAbstractItemView, QProgressBar,
    QInputDialog, QCheckBox, QSplitter, QSizePolicy # Import QSizePolicy
)
from PyQt6.QtGui import QFont, QDragEnterEvent, QDropEvent, QPalette, QColor, QPainter, QPen, QBrush
from PyQt6.QtCore import Qt, QThread, QObject, pyqtSignal, QRect, QTimer, QThreadPool, QRunnable, pyqtSlot

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  UI COLORS, CONSTANTS & WORKERS (Now in Global Scope)
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
COLOR_UNIQUE = QColor(255, 0, 0, 40)
COLOR_DIFFERENT = QColor(255, 165, 0, 50)
COLOR_IDENTICAL = QColor(0, 128, 0, 40)
STATUS_UNIQUE, STATUS_DIFFERENT, STATUS_IDENTICAL = 0, 1, 2
FILTER_HIGHLIGHT_STYLE = "background-color: #05B8CC; color: black;"

class FileHasher(QRunnable): # QRunnable is not a QObject, so it cannot have pyqtSignals directly
    def __init__(self, relative_path, full_path, callback):
        super().__init__()
        self.relative_path = relative_path
        self.full_path = full_path
        self.callback = callback # This will be a callable from the SnapshotWorker
        self._is_running = True

    def run(self):
        try:
            if not self._is_running: return
            hasher = hashlib.md5()
            with open(self.full_path, 'rb') as f:
                while chunk := f.read(8192):
                    if not self._is_running: return
                    hasher.update(chunk)
            self.callback(hasher.hexdigest(), None) # Pass hash and no error. The relative_path is already part of the partial.
        except Exception as e:
            self.callback(None, str(e)) # Pass no hash and an error.
        finally:
            pass # QRunnable is auto-deleted, no need for a 'finished' signal from here

    def stop(self):
        self._is_running = False

class SnapshotWorker(QObject):
    finished = pyqtSignal()
    error = pyqtSignal(str)
    progress = pyqtSignal(int, int)

    def __init__(self, folder_path, snapshot_path, filenames_with_paths):
        super().__init__()
        self.folder_path, self.snapshot_path = folder_path, snapshot_path
        self.filenames_with_paths = filenames_with_paths # Now receives (relative_path, full_path) tuples
        self._is_running = True
        self.file_hashes = {}
        self.processed_count = 0
        self.total_count = len(self.filenames_with_paths)
        self.threadpool = QThreadPool.globalInstance()
        self.threadpool.setMaxThreadCount(os.cpu_count() or 2)
        self.active_tasks = 0 # To track pending tasks

    def run(self):
        print(f"CMD_LOG: [SnapshotWorker] Hashing {self.total_count} files for {self.folder_path}.")
        if self.total_count == 0:
            self._finalize()
            return

        self.processed_count = 0
        self.file_hashes = {}

        self.active_tasks = self.total_count # Initialize active tasks count

        for relative_path, full_path in self.filenames_with_paths:
            if not self._is_running:
                # QThreadPool doesn't have a clear() method for pending tasks.
                # Submitted tasks will run, but their results will be ignored by _on_hash_result_callback
                break
            
            # Pass a partial function as callback
            hasher = FileHasher(relative_path, full_path, partial(self._on_hash_result_callback, relative_path))
            hasher.setAutoDelete(True)
            self.threadpool.start(hasher)

    @pyqtSlot(str, str, str) # relative_path, hash_result, error_msg
    def _on_hash_result_callback(self, relative_path, hash_result, error_msg=None):
        if not self._is_running: return

        if hash_result:
            self.file_hashes[relative_path] = hash_result
        elif error_msg:
            print(f"CMD_LOG: [SnapshotWorker] WARNING: Could not hash file '{relative_path}': {error_msg}")

        self.processed_count += 1
        self.progress.emit(self.processed_count, self.total_count)
        
        self.active_tasks -= 1
        if self.active_tasks == 0:
            self._finalize()

    def _finalize(self):
        if not self._is_running: return
        print(f"CMD_LOG: [SnapshotWorker] Prepared {len(self.file_hashes)} hashes out of {self.total_count} for {self.folder_path}.")
        snapshot_data = {'path': self.folder_path, 'files': self.file_hashes}
        try:
            with open(self.snapshot_path, 'w', encoding='utf-8') as f:
                json.dump(snapshot_data, f)
            print(f"CMD_LOG: [SnapshotWorker] Finished writing snapshot for {self.folder_path}.")
            self.finished.emit()
        except Exception as e:
            self.error.emit(f"Could not write snapshot file: {e}")

    def stop(self): self._is_running = False

class ContentLoaderWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    def __init__(self, folder_path, recursive, extensions=None):
        super().__init__()
        self.folder_path, self.recursive, self.extensions = folder_path, recursive, extensions
        self._is_running = True
    def run(self):
        print(f"CMD_LOG: [ContentLoaderWorker] Running for {self.folder_path} (Recursive: {self.recursive}).")
        try:
            results = []
            if self.recursive:
                for dirpath, _, filenames in os.walk(self.folder_path):
                    if not self._is_running: break
                    for item_name in filenames:
                        if not self._is_running: break
                        ext = os.path.splitext(item_name)[1].lower()
                        if self.extensions and ext not in self.extensions: continue
                        full_path = os.path.join(dirpath, item_name)
                        relative_path = os.path.relpath(full_path, self.folder_path)
                        stats = os.stat(full_path); results.append((relative_path, stats, ext, False, full_path)) # Added full_path
            else:
                for item_name in os.listdir(self.folder_path):
                    if not self._is_running: break
                    full_path = os.path.join(self.folder_path, item_name)
                    stats, is_dir = os.stat(full_path), os.path.isdir(full_path)
                    ext = os.path.splitext(item_name)[1].lower() if not is_dir else ''
                    if self.extensions and not is_dir and ext not in self.extensions: continue
                    results.append((item_name, stats, ext, is_dir, full_path)) # Added full_path
            if self._is_running: self.finished.emit(results)
            print(f"CMD_LOG: [ContentLoaderWorker] Finished for {self.folder_path}. Found {len(results)} items.")
        except Exception as e:
            print(f"CMD_LOG: [ContentLoaderWorker] ERROR for {self.folder_path}: {e}")
            if self._is_running: self.error.emit(f"Could not load content: {e}")
    def stop(self): self._is_running = False

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  ANIMATED LOADING OVERLAY
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class LoadingOverlay(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setVisible(False)
        self.angle = 0
        self.timer = QTimer(self, interval=20)
        self.timer.timeout.connect(self.update_animation)
        self.base_text = "Loading..."

        layout = QVBoxLayout(self)
        layout.addStretch()
        self.label = QLabel("Loading..."); self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label.setStyleSheet("color: white; font-size: 14px; font-weight: bold;"); layout.addWidget(self.label)
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar { border: 2px solid grey; border-radius: 5px; text-align: center; background-color: #2a2a2a; color: white; }
            QProgressBar::chunk { background-color: #05B8CC; width: 10px; margin: 0.5px; }
        """); self.progress_bar.setVisible(False); layout.addWidget(self.progress_bar); layout.addStretch()
    def paintEvent(self, event):
        painter = QPainter(self)
        if not painter.isActive(): return # Ensure painter is active
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(QBrush(QColor(0, 0, 0, 180))); painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(self.rect())
        painter.translate(self.rect().center())
        painter.setPen(QPen(QColor("#05B8CC"), 10, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        radius = min(self.width(), self.height()) / 8
        arc_rect = QRect(int(-radius), int(-radius), int(radius*2), int(radius*2))
        painter.rotate(self.angle)
        painter.drawArc(arc_rect, 0, 90 * 16)
    def start_animation(self, text="Discovering..."):
        self.base_text = text
        self.label.setText(text) # Set text directly, not self.base_text
        self.progress_bar.setVisible(False)
        self.resize(self.parent().size())
        self.setVisible(True)
        self.raise_()
        QApplication.processEvents()
        self.timer.start()
    def stop_animation(self):
        self.timer.stop()
        self.setVisible(False)
    def set_progress(self, current, total):
        if total <= 0: return
        percentage = int((current/total)*100) if total > 0 else 0
        self.label.setText(f"{self.base_text} {percentage}%")
        if not self.progress_bar.isVisible(): self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(total); self.progress_bar.setValue(current)
        self.progress_bar.setFormat(f"{percentage}%")

    def update_animation(self):
        self.angle = (self.angle + 10) % 360
        self.update()

class BatchComparisonWorker(QObject):
    progress = pyqtSignal(int, int)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    def __init__(self, left_hashes, right_hashes, common_filenames):
        super().__init__()
        self.left_hashes = left_hashes
        self.right_hashes = right_hashes
        self.common_filenames = common_filenames
        self._is_running = True
    def run(self):
        try:
            diff_files, total = [], len(self.common_filenames)
            for i, filename in enumerate(self.common_filenames):
                if not self._is_running: break
                if self.left_hashes.get(filename) != self.right_hashes.get(filename):
                    diff_files.append(filename)
                if i % 5 == 0 or i == total - 1: self.progress.emit(i + 1, total)
            if self._is_running:
                identical_files = [f for f in self.common_filenames if f not in diff_files]
                self.finished.emit({'diff_files': diff_files, 'identical_files': identical_files})
        except Exception as e: self.error.emit(f"Comparison failed: {e}")
    def stop(self): self._is_running = False

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  CustomTableWidgetItem & REPORT WINDOW
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class CustomTableWidgetItem(QTableWidgetItem):
    def __lt__(self, other):
        data_self, data_other = self.data(Qt.ItemDataRole.UserRole), other.data(Qt.ItemDataRole.UserRole)
        if data_self is not None and data_other is not None and isinstance(data_self, (int, float, datetime)): return data_self < data_other
        return super().__lt__(other)

class ReportWindow(QWidget):
    def __init__(self, title, report_text):
        super().__init__(); self.report_text = report_text; self.setWindowTitle(title); self.setGeometry(250, 250, 800, 600)
        layout = QVBoxLayout(self)
        self.text_edit = QTextEdit(self.report_text); self.text_edit.setFont(QFont("Consolas", 10)); self.text_edit.setReadOnly(True)
        layout.addWidget(self.text_edit); button_layout = QHBoxLayout()
        self.save_button = QPushButton("Save Report"); self.save_button.clicked.connect(self.save_report)
        button_layout.addStretch(); button_layout.addWidget(self.save_button); layout.addLayout(button_layout)
    def save_report(self):
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Report", "comparison_report.txt", "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f: f.write(self.report_text)
                QMessageBox.information(self, "Success", "Report saved successfully.")
            except Exception as e: QMessageBox.critical(self, "Error", f"Could not save report:\n{e}")

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  FILE PANEL WIDGET
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class FilePanel(QFrame):
    selection_changed = pyqtSignal()
    snapshot_ready = pyqtSignal()
    hashing_progress = pyqtSignal(int, int)
    STATUS_COLUMN = 4
    
    def __init__(self, side_name, main_window):
        super().__init__()
        print(f"CMD_LOG: FilePanel '{side_name}' created.")
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.side_name, self.main_window = side_name, main_window
        self.content_type, self.folder_path, self.file_path, self.snapshot_path = None, None, None, None
        self.thread, self.worker, self.is_busy = None, None, False
        self.loaded_files_data = None # Store loaded file data for on-demand snapshot creation
        self.snapshot_finished_internal = False # New flag to track worker completion
        self.current_extensions, self.active_filter_button = None, None
        panel_layout = QVBoxLayout(self)
        top_bar_layout = QHBoxLayout(); panel_layout.addLayout(top_bar_layout)
        filter_bar_layout = QHBoxLayout(); panel_layout.addLayout(filter_bar_layout)
        self.path_label = QLabel("No content loaded.")
        self.path_label.setStyleSheet("padding: 4px; background-color: #2a2a2a; border-radius: 4px; font-style: italic;")
        self.path_label.setAlignment(Qt.AlignmentFlag.AlignCenter); panel_layout.addWidget(self.path_label)
        self.stacked_layout = QStackedLayout()
        self.table_widget, self.text_viewer, self.add_prompt_button = self._create_table_widget(), self._create_text_viewer(), self._create_prompt_button()
        self.stacked_layout.addWidget(self.table_widget); self.stacked_layout.addWidget(self.text_viewer); self.stacked_layout.addWidget(self.add_prompt_button)
        panel_layout.addLayout(self.stacked_layout); self.stacked_layout.setCurrentWidget(self.add_prompt_button)
        bottom_layout = QHBoxLayout()
        self.status_label = QLabel(""); self.status_label.setStyleSheet("font-style: italic;"); bottom_layout.addWidget(self.status_label)
        bottom_layout.addStretch()
        self.cancel_button = QPushButton("Clear Panel"); self.cancel_button.clicked.connect(self.clear_panel); self.cancel_button.setVisible(False)
        bottom_layout.addWidget(self.cancel_button); panel_layout.addLayout(bottom_layout)
        self._create_buttons(top_bar_layout, filter_bar_layout)
        self.setAcceptDrops(True); self.loading_overlay = LoadingOverlay(self)

        # Set a minimum width for the FilePanel to prevent excessive expansion
        # This value should be carefully chosen based on your UI design and screen size
        self.setMinimumWidth(400) # Example: 400 pixels minimum width for each panel

    def resizeEvent(self, event):
        self.loading_overlay.resize(event.size()); super().resizeEvent(event)
    
    def clear_panel(self):
        print(f"CMD_LOG: [{self.side_name}] Clearing panel.")
        self.cleanup_thread()
        self.content_type, self.folder_path, self.file_path, self.snapshot_path = None, None, None, None
        self.path_label.setText("No content loaded."); self.status_label.setText("")
        self.loaded_files_data = None # Clear loaded data
        self.snapshot_finished_internal = False # Reset flag
        self.stacked_layout.setCurrentWidget(self.add_prompt_button)
        self.cancel_button.setVisible(False)
        self.clear_highlighting(); self.main_window.on_panel_cleared()

    def _create_table_widget(self):
        table = QTableWidget(); table.setColumnCount(5); headers = ["Name", "Last Modified", "Type", "Size", "Status"]
        table.setHorizontalHeaderLabels(headers); table.setColumnHidden(self.STATUS_COLUMN, True)
        table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        table.verticalHeader().setVisible(False)
        table.horizontalHeader().setSortIndicatorShown(True)
        
        # Set size policy to ensure the table can expand but also shrink
        # This is crucial for preventing the window from expanding beyond screen limits
        table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        table.setMinimumHeight(100) # Give it a reasonable minimum height
        
        table.horizontalHeader().sectionClicked.connect(self.sort_by_column)
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        
        # Set fixed widths and fixed resize mode for other columns to prevent resizing
        table.setColumnWidth(1, 150) # Last Modified
        table.setColumnWidth(2, 100) # Type
        table.setColumnWidth(3, 80)  # Size
        table.setColumnWidth(4, 80)  # Status
        table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        
        table.setSortingEnabled(True); table.itemSelectionChanged.connect(self.selection_changed.emit); return table

    def _create_text_viewer(self):
        viewer = QTextEdit(); viewer.setReadOnly(True); viewer.setFont(QFont("Consolas", 10)); return viewer

    def _create_prompt_button(self):
        button = QPushButton(f"+\n\nDrag & Drop Folder or Text File")
        button.setFont(QFont("Segoe UI", 18)); button.setStyleSheet("color: #6a717b; border: 2px dashed #6a717b;")
        button.clicked.connect(self.add_folder); return button

    def _create_buttons(self, top_bar, filter_bar):
        self.btn_add = QPushButton("Add Folder"); self.btn_add.clicked.connect(self.add_folder); top_bar.addWidget(self.btn_add)
        self.btn_save = QPushButton("Save List"); self.btn_save.clicked.connect(self.save_file_list); top_bar.addWidget(self.btn_save)
        self.recursive_checkbox = QCheckBox("Subfolders"); self.recursive_checkbox.setChecked(True)
        self.recursive_checkbox.stateChanged.connect(self.reload_content)
        filter_bar.addWidget(self.recursive_checkbox); filter_bar.addWidget(QLabel("Filters:"))
        self.filter_buttons = {
            'all': QPushButton("All"), 'images': QPushButton("Images"), 'docs': QPushButton("Documents"), 
            'audio': QPushButton("Audio"), 'video': QPushButton("Video"), 'custom': QPushButton("Custom...")
        }
        self.filter_buttons['all'].clicked.connect(lambda: self.apply_extension_filter(self.filter_buttons['all']))
        self.filter_buttons['images'].clicked.connect(lambda: self.apply_extension_filter(self.filter_buttons['images'], ['.png', '.jpg', '.jpeg', '.gif', '.bmp']))
        self.filter_buttons['docs'].clicked.connect(lambda: self.apply_extension_filter(self.filter_buttons['docs'], ['.txt', '.pdf', '.doc', '.docx']))
        self.filter_buttons['audio'].clicked.connect(lambda: self.apply_extension_filter(self.filter_buttons['audio'], ['.mp3', '.wav', '.ogg', '.flac']))
        self.filter_buttons['video'].clicked.connect(lambda: self.apply_extension_filter(self.filter_buttons['video'], ['.mp4', '.mkv', '.avi', '.mov']))
        self.filter_buttons['custom'].clicked.connect(self.show_custom_filter_dialog)
        for btn in self.filter_buttons.values(): filter_bar.addWidget(btn)
        filter_bar.addStretch()

    def load_content(self):
        if not self.folder_path or self.is_busy: return
        print(f"CMD_LOG: [{self.side_name}] Starting load_content for: {self.folder_path}")
        self.is_busy = True; self.main_window.on_panel_cleared()
        self.loading_overlay.start_animation("Loading...")
        self.cleanup_thread()
        self.thread = QThread()
        is_recursive = self.recursive_checkbox.isChecked()
        self.worker = ContentLoaderWorker(self.folder_path, is_recursive, self.current_extensions)
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_loading_finished)
        self.worker.error.connect(self.on_loading_error)
        self.thread.start()

    def on_loading_finished(self, results):
        print(f"CMD_LOG: [{self.side_name}] Finished loading content. {len(results)} items found.")
        self.table_widget.setSortingEnabled(False); self.table_widget.setRowCount(0)
        # Store the full results for snapshot creation later
        self.loaded_files_data = results 
        for name, stats, ext, is_dir, _ in results: # Unpack, ignore full_path for table display
            self.add_file_to_table(name, stats, ext, is_dir)
        self.table_widget.setSortingEnabled(True); self.stacked_layout.setCurrentWidget(self.table_widget)
        self.status_label.setText(f"{len(results)} items found.")
        self.cancel_button.setVisible(True); self.is_busy = False
        self.loading_overlay.stop_animation()
        
        # Restore the splitter state to prevent window expansion after loading content
        # This is crucial to override any size hints from the table after population
        if self.main_window.initial_splitter_state:
            self.main_window.splitter.restoreState(self.main_window.initial_splitter_state)
            
        self.main_window.update_button_states()
        # No longer call create_snapshot here. It's done on demand by compare_folders.

    def on_loading_error(self, err_msg):
        QMessageBox.critical(self, "Error", err_msg); self.is_busy = False
        self.loading_overlay.stop_animation(); self.main_window.update_button_states()
    
    def cleanup_thread(self):
        if self.thread:
            if self.thread.isRunning():
                if self.worker:
                    self.worker.stop() # Request worker to stop
                self.thread.quit() # Ask thread to quit its event loop
                self.thread.wait(5000) # Wait up to 5 seconds for the thread to finish
                if self.thread.isRunning():
                    print(f"CMD_LOG: [{self.side_name}] Warning: Thread did not terminate gracefully.")
            self.thread.deleteLater() # Schedule for deletion
            self.thread = None
            self.worker = None

    def create_snapshot(self, loaded_files_data): # Now accepts the loaded data
        self.snapshot_path = None
        self.cleanup_thread() # Ensure any previous snapshot worker is stopped
        self.snapshot_finished_internal = False # Reset flag
        snapshot_filename = hashlib.md5(self.folder_path.encode()).hexdigest() + ".json"
        print(f"CMD_LOG: [{self.side_name}] Creating snapshot: {snapshot_filename}")
        self.snapshot_path = os.path.join(self.main_window.cache_dir, snapshot_filename)
        # Prepare data for SnapshotWorker: (display_name, full_path)
        files_for_hashing = [(res[0], res[4]) for res in loaded_files_data if not res[3]] # Filter out directories
        self.thread = QThread()
        self.worker = SnapshotWorker(self.folder_path, self.snapshot_path, files_for_hashing)
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self._on_snapshot_worker_finished)
        self.worker.error.connect(self._on_snapshot_worker_error)
        self.worker.progress.connect(self.loading_overlay.set_progress)
        self.worker.finished.connect(self.thread.quit)
        self.worker.error.connect(self.thread.quit)
        self.thread.finished.connect(self.thread.deleteLater)
        self.worker.finished.connect(self.worker.deleteLater)
        self.thread.start()

        self.worker.progress.connect(self.hashing_progress.emit)
    def _on_snapshot_worker_finished(self):
        print(f"CMD_LOG: [{self.side_name}] Snapshot worker finished successfully.")
        self.snapshot_finished_internal = True
        self.snapshot_ready.emit()

    def _on_snapshot_worker_error(self, err_msg):
        print(f"CMD_LOG: [{self.side_name}] Snapshot worker error: {err_msg}")
        self.snapshot_path = None # Invalidate path on error
        self.snapshot_finished_internal = True # It's finished, albeit with an error
        self.snapshot_ready.emit() # Emit public signal for main window
        QMessageBox.critical(self, "Snapshot Error", f"Error creating snapshot for {self.side_name} panel:\n{err_msg}")

    def load_text_file(self, file_path):
        if self.is_busy: return
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f: content = f.read()
            self.text_viewer.setPlainText(content)
            self.content_type, self.folder_path, self.file_path = 'file', None, file_path
            self.path_label.setText(file_path); self.stacked_layout.setCurrentWidget(self.text_viewer)
            self.cancel_button.setVisible(True)
        except Exception as e: QMessageBox.critical(self, "Error", f"Could not read file:\n{e}"); self.clear_panel()
        self.main_window.update_button_states()

    def add_file_to_table(self, name, stats, ext, is_dir):
        mod_time, size_bytes = datetime.fromtimestamp(stats.st_mtime), stats.st_size
        items = [CustomTableWidgetItem(text) for text in [name, mod_time.strftime('%Y-%m-%d %H:%M'), "Folder" if is_dir else f"{ext.upper()} File", "" if is_dir else self.format_size(size_bytes)]]
        items[0].setData(Qt.ItemDataRole.UserRole, name); items[1].setData(Qt.ItemDataRole.UserRole, mod_time)
        items[3].setData(Qt.ItemDataRole.UserRole, size_bytes if not is_dir else -1)
        row_pos = self.table_widget.rowCount(); self.table_widget.insertRow(row_pos)
        for i, item in enumerate(items): self.table_widget.setItem(row_pos, i, item)

    def format_size(self, size_bytes):
        if size_bytes <= 0: return "0 B"
        size_names = ("B", "KB", "MB", "GB", "TB"); i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i); return f"{round(size_bytes / p, 2)} {size_names[i]}"

    def sort_by_column(self, col_index): self.table_widget.sortItems(col_index, self.table_widget.horizontalHeader().sortIndicatorOrder())
    
    def add_folder(self):
        if self.is_busy: return
        folder_path = QFileDialog.getExistingDirectory(self, "Select a Folder")
        if folder_path: self.folder_path = folder_path; self.content_type = 'folder'; self.path_label.setText(folder_path); self.load_content()

    def reload_content(self):
        if self.content_type == 'folder': self.apply_extension_filter(self.active_filter_button, self.current_extensions)
    
    def apply_extension_filter(self, button_pressed, extensions=None):
        if self.active_filter_button: self.active_filter_button.setStyleSheet("")
        if button_pressed: button_pressed.setStyleSheet(FILTER_HIGHLIGHT_STYLE)
        self.active_filter_button = button_pressed
        if self.content_type == 'folder': self.current_extensions = extensions; self.load_content()

    def show_custom_filter_dialog(self):
        if not self.folder_path or self.is_busy: return
        text, ok = QInputDialog.getText(self, 'Custom Filter', 'Enter extensions, separated by commas:', text=".zip, .rar")
        if ok and text:
            extensions = [f".{ext.strip().lstrip('.')}" for ext in text.split(',') if ext.strip()]
            self.apply_extension_filter(self.filter_buttons['custom'], extensions)

    def save_file_list(self):
        if self.content_type != 'folder' or self.table_widget.rowCount() == 0: return
        current_name = os.path.basename(os.path.normpath(self.folder_path)).replace(' ', '_')
        parent_name = os.path.basename(os.path.dirname(self.folder_path)).replace(' ', '_')
        default_name = f"{parent_name}-{current_name}-list.txt" if parent_name else f"{current_name}-list.txt"
        try:
            save_dir = os.path.join(os.path.dirname(os.path.realpath(__file__)), "save_list")
            os.makedirs(save_dir, exist_ok=True)
        except Exception: save_dir = ""
        file_path, _ = QFileDialog.getSaveFileName(self, "Save File List", os.path.join(save_dir, default_name), "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    headers = [self.table_widget.horizontalHeaderItem(i).text() for i in range(self.table_widget.columnCount())]
                    f.write("\t".join(headers) + "\n")
                    for row in range(self.table_widget.rowCount()):
                        row_data = [self.table_widget.item(row, col).text() if self.table_widget.item(row, col) else "" for col in range(self.table_widget.columnCount())]
                        f.write("\t".join(row_data) + "\n")
                QMessageBox.information(self, "Success", "Report saved successfully.")
            except Exception as e: QMessageBox.critical(self, "Error", f"Could not save file:\n{e}")

    def get_selected_filenames(self):
        return [self.table_widget.item(index.row(), 0).text() for index in self.table_widget.selectedIndexes() if index.column() == 0]

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls(): event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        if self.is_busy: return
        for url in event.mimeData().urls():
            if url.isLocalFile():
                path = url.toLocalFile()
                print(f"CMD_LOG: [{self.side_name}] Drop event detected for path: {path}")
                if os.path.isdir(path): self.folder_path = path; self.content_type = 'folder'; self.path_label.setText(path); self.load_content(); break
                elif os.path.isfile(path) and path.lower().endswith('.txt'): self.load_text_file(path); break

    def apply_comparison_highlighting(self, unique_files, identical_files, diff_files):
        self.clear_highlighting(); self.table_widget.setColumnHidden(self.STATUS_COLUMN, True) # Keep hidden by default
        for row in range(self.table_widget.rowCount()):
            filename = self.table_widget.item(row, 0).text()
            status_item = CustomTableWidgetItem(); color, status_text, status_data = None, "", -1
            if filename in unique_files: color, status_text, status_data = COLOR_UNIQUE, "Unique", STATUS_UNIQUE
            elif filename in diff_files: color, status_text, status_data = COLOR_DIFFERENT, "Different", STATUS_DIFFERENT
            elif filename in identical_files: color, status_text, status_data = COLOR_IDENTICAL, "Identical", STATUS_IDENTICAL
            if color:
                for col in range(self.STATUS_COLUMN): self.table_widget.item(row, col).setBackground(QBrush(color))
                status_item.setText(status_text); status_item.setData(Qt.ItemDataRole.UserRole, status_data)
                self.table_widget.setItem(row, self.STATUS_COLUMN, status_item)
        self.table_widget.setColumnHidden(self.STATUS_COLUMN, False) # Show column after applying colors
    
    def clear_highlighting(self):
        self.table_widget.setColumnHidden(self.STATUS_COLUMN, True)
        for row in range(self.table_widget.rowCount()):
            for col in range(self.table_widget.columnCount()):
                if self.table_widget.item(row, col): self.table_widget.item(row, col).setBackground(QBrush(Qt.GlobalColor.transparent))

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  MAIN WINDOW
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
class ModernFileManager(QMainWindow):
    def __init__(self):
        super().__init__()
        print("CMD_LOG: ModernFileManager.__init__ - Start")
        self.setWindowTitle("Advanced Python File Manager")
        self.cache_dir = os.path.join(os.path.dirname(os.path.realpath(__file__)), ".cache")
        print(f"CMD_LOG: Cache directory set to: {self.cache_dir}")
        os.makedirs(self.cache_dir, exist_ok=True)
        main_widget = QWidget(); self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)
        self.left_panel, self.right_panel = FilePanel("Left", self), FilePanel("Right", self)
        
        # --- Use QSplitter for stable layout ---
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        center_widget = self.create_center_panel() # create_center_panel now returns the widget
        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(center_widget)
        self.splitter.addWidget(self.right_panel)

        # Set initial sizes to be proportional and prevent collapsing
        self.splitter.setStretchFactor(0, 1) # Left panel stretches
        self.splitter.setStretchFactor(1, 0) # Center panel does not stretch
        self.splitter.setStretchFactor(2, 1) # Right panel stretches
        self.splitter.setCollapsible(0, False)
        self.splitter.setCollapsible(1, False)
        self.splitter.setCollapsible(2, False)
        main_layout.addWidget(self.splitter)
        # --- END NEW ---

        self.initial_splitter_state = None # To store the correct splitter layout
        self.last_folder_comparison_results, self.comparison_thread, self.comparison_worker, self.report_win = None, None, None, None
        self.hashing_progress = {'Left': (0, 1), 'Right': (0, 1)}
        self.left_panel.selection_changed.connect(self.update_button_states)
        self.right_panel.selection_changed.connect(self.update_button_states)
        self.left_panel.snapshot_ready.connect(self.update_button_states)
        self.left_panel.hashing_progress.connect(partial(self.on_hashing_progress, 'Left'))
        self.right_panel.hashing_progress.connect(partial(self.on_hashing_progress, 'Right'))

        self.right_panel.snapshot_ready.connect(self.update_button_states)
        print("CMD_LOG: ModernFileManager.__init__ - End")
        self.update_button_states()
        self.showMaximized() # Open maximized by default

    def showEvent(self, event):
        """Set initial splitter sizes only the first time the window is shown."""
        super().showEvent(event)
        if not hasattr(self, '_splitter_sized'):
            self._splitter_sized = True
            # Wait for the event loop to process the show/maximize event before getting sizes
            QTimer.singleShot(0, self.set_initial_splitter_sizes)

    def set_initial_splitter_sizes(self):
        """Calculates and sets the initial sizes for the splitter."""
        total_width = self.splitter.width()
        center_width = self.splitter.widget(1).width()
        panel_width = (total_width - center_width) // 2
        self.splitter.setSizes([panel_width, center_width, panel_width])
        self.initial_splitter_state = self.splitter.saveState()

    def create_center_panel(self):
        center_frame = QFrame(); center_layout = QVBoxLayout(center_frame); center_layout.addStretch()
        center_frame.setFixedWidth(150)
        self.btn_compare_folders = QPushButton("Compare\nFolders"); center_layout.addWidget(self.btn_compare_folders)
        self.btn_move_selected_to_right = QPushButton("Move Selected ->"); self.btn_move_selected_to_right.setVisible(False); center_layout.addWidget(self.btn_move_selected_to_right)
        self.btn_move_selected_to_left = QPushButton("<- Move Selected"); self.btn_move_selected_to_left.setVisible(False); center_layout.addWidget(self.btn_move_selected_to_left)
        self.btn_move_missing_to_right = QPushButton("Move Missing ->"); center_layout.addWidget(self.btn_move_missing_to_right)
        self.btn_move_missing_to_left = QPushButton("<- Move Missing"); center_layout.addWidget(self.btn_move_missing_to_left)
        self.btn_save_report = QPushButton("Save Full Report"); center_layout.addWidget(self.btn_save_report)
        self.btn_compare_text = QPushButton("Compare\nText Files"); center_layout.addWidget(self.btn_compare_text)
        center_layout.addStretch()
        self.btn_compare_folders.clicked.connect(self.compare_folders)
        self.btn_move_selected_to_right.clicked.connect(lambda: self.move_selected('left_to_right'))
        self.btn_move_selected_to_left.clicked.connect(lambda: self.move_selected('right_to_left'))
        self.btn_move_missing_to_right.clicked.connect(lambda: self.move_missing('left_to_right'))
        self.btn_move_missing_to_left.clicked.connect(lambda: self.move_missing('right_to_left'))
        self.btn_save_report.clicked.connect(self.show_folder_comparison_report)
        self.btn_compare_text.clicked.connect(self.compare_text_files)
        return center_frame

    def on_panel_cleared(self):
        self.last_folder_comparison_results = None
        self.left_panel.clear_highlighting(); self.right_panel.clear_highlighting()
        if self.left_panel.content_type == 'folder': self.left_panel.status_label.setText(f"{self.left_panel.table_widget.rowCount()} items found.")
        if self.right_panel.content_type == 'folder': self.right_panel.status_label.setText(f"{self.right_panel.table_widget.rowCount()} items found.")
        self.update_button_states()

    def update_button_states(self):
        folder_mode = self.left_panel.content_type == 'folder' and self.right_panel.content_type == 'folder'
        file_mode = self.left_panel.content_type == 'file' and self.right_panel.content_type == 'file'
        busy = self.left_panel.is_busy or self.right_panel.is_busy
        
        # Enable compare folders if both panels are in folder mode and not busy,
        # and they have loaded their file data (even if snapshots aren't created yet)
        can_compare_folders = folder_mode and not busy and \
                              self.left_panel.loaded_files_data is not None and \
                              self.right_panel.loaded_files_data is not None
        self.btn_compare_folders.setEnabled(can_compare_folders)

        self.btn_compare_text.setEnabled(file_mode and not busy)
        comp_active = self.last_folder_comparison_results is not None
        self.btn_move_missing_to_right.setVisible(comp_active)
        self.btn_move_missing_to_left.setVisible(comp_active)
        self.btn_move_selected_to_right.setVisible(folder_mode and not comp_active)
        self.btn_save_report.setVisible(comp_active) # Show save report button after comparison
        self.btn_move_selected_to_left.setVisible(folder_mode and not comp_active)
        self.btn_move_selected_to_right.setEnabled(folder_mode and len(self.left_panel.get_selected_filenames()) > 0)
        self.btn_move_selected_to_left.setEnabled(folder_mode and len(self.right_panel.get_selected_filenames()) > 0)
        for panel in [self.left_panel, self.right_panel]:
            is_folder_mode = panel.content_type == 'folder'
            panel.btn_save.setEnabled(is_folder_mode)
            panel.recursive_checkbox.setEnabled(is_folder_mode)
            for btn in panel.filter_buttons.values(): btn.setEnabled(is_folder_mode)

    def compare_folders(self): # This is now the trigger
        if self.left_panel.is_busy or self.right_panel.is_busy: return

        if not (self.left_panel.loaded_files_data and self.right_panel.loaded_files_data):
            QMessageBox.warning(self, "Comparison Error", "Please load folders into both panels first.")
            return

        print("\nCMD_LOG: Starting folder comparison...")
        self.on_panel_cleared()

        self.hashing_progress = {'Left': (0, 1), 'Right': (0, 1)}
        self.snapshots_ready_count = 0

        self.left_panel.loading_overlay.start_animation("Hashing files...")
        self.right_panel.loading_overlay.start_animation("Hashing files...")
        QApplication.processEvents()
        
        # Connect snapshot_ready signals to _on_snapshot_ready for coordination
        # Disconnect/reconnect logic is handled within _on_snapshot_ready now
        self.left_panel.snapshot_ready.connect(self._on_snapshot_ready) 
        self.right_panel.snapshot_ready.connect(self._on_snapshot_ready)

        self.left_panel.create_snapshot(self.left_panel.loaded_files_data)
        self.right_panel.create_snapshot(self.right_panel.loaded_files_data)

    def _on_snapshot_ready(self):
        self.snapshots_ready_count += 1
        if self.snapshots_ready_count < 2:
            return # Still waiting for the other panel

        sys.stdout.write('\n') # Newline after hashing progress bar
        sys.stdout.flush()
        print("CMD_LOG: Both snapshots are ready. Starting content comparison.")

        # Reset and disconnect to prevent re-triggering
        self.snapshots_ready_count = 0
        try: self.left_panel.snapshot_ready.disconnect(self._on_snapshot_ready)
        except TypeError: pass
        try: self.right_panel.snapshot_ready.disconnect(self._on_snapshot_ready)
        except TypeError: pass

        if not (self.left_panel.snapshot_path and self.right_panel.snapshot_path and \
                os.path.exists(self.left_panel.snapshot_path) and os.path.exists(self.right_panel.snapshot_path)):
            self.on_comparison_error("One or both snapshot files were not successfully created. Cannot proceed.")
            return

        try:
            with open(self.left_panel.snapshot_path, 'r') as f: left_data = json.load(f)
            with open(self.right_panel.snapshot_path, 'r') as f: right_data = json.load(f)

            left_file_hashes = left_data['files']
            right_file_hashes = right_data['files']

            left_filenames = set(left_file_hashes.keys())
            right_filenames = set(right_file_hashes.keys())

            print(f"CMD_LOG: Found {len(left_filenames)} files in left snapshot, {len(right_filenames)} in right snapshot.")
        except Exception as e:
            self.on_comparison_error(f"Could not read snapshot files: {e}")
            return

        common_filenames = list(left_filenames & right_filenames)
        print(f"CMD_LOG: Comparing {len(common_filenames)} common files based on hashes.")
        self.last_folder_comparison_results = {'left_only': list(left_filenames - right_filenames), 'right_only': list(right_filenames - left_filenames)}

        if not common_filenames:
            self.on_folder_comparison_finished({'diff_files': [], 'identical_files': []})
            return

        self.left_panel.loading_overlay.start_animation("Comparing...")
        self.right_panel.loading_overlay.start_animation("Comparing...")

        self.comparison_thread = QThread()
        self.comparison_worker = BatchComparisonWorker(left_file_hashes, right_file_hashes, common_filenames)
        self.comparison_worker.moveToThread(self.comparison_thread)
        self.comparison_thread.started.connect(self.comparison_worker.run)
        self.comparison_worker.progress.connect(self.on_comparison_progress)
        self.comparison_worker.finished.connect(self.on_folder_comparison_finished)
        self.comparison_worker.error.connect(self.on_comparison_error)
        self.comparison_thread.start()

    def on_hashing_progress(self, side, current, total):
        self.hashing_progress[side] = (current, total)

        # Combine total progress
        total_current = self.hashing_progress['Left'][0] + self.hashing_progress['Right'][0]
        total_max = self.hashing_progress['Left'][1] + self.hashing_progress['Right'][1]

        if total_max <= 2: # Initial state before totals are known
             return

        # Terminal Progress Bar for Hashing
        length = 50
        fill = '█'
        percent = ("{0:.1f}").format(100 * (total_current / float(total_max)))
        filled_length = int(length * total_current // total_max)
        bar = fill * filled_length + '-' * (length - filled_length)
        
        sys.stdout.write(f'\rCMD_LOG: Hashing... |{bar}| {percent}% Complete'.ljust(80)) # ljust to clear previous line
        sys.stdout.flush()

    def on_comparison_progress(self, current, total):
        self.left_panel.loading_overlay.set_progress(current, total)
        self.right_panel.loading_overlay.set_progress(current, total)
        # CMD Progress Bar
        length = 50
        fill = '█'
        percent = ("{0:.1f}").format(100 * (current / float(total)))
        filled_length = int(length * current // total)
        bar = fill * filled_length + '-' * (length - filled_length)
        sys.stdout.write(f'\rCMD_LOG: Comparing... |{bar}| {percent}% Complete')
        sys.stdout.flush()

    def on_folder_comparison_finished(self, results):
        sys.stdout.write('\n') # Newline after progress bar
        sys.stdout.flush()
        print("CMD_LOG: Folder comparison finished.")
        self.last_folder_comparison_results.update(results)
        self.left_panel.loading_overlay.stop_animation(); self.right_panel.loading_overlay.stop_animation()
        self.left_panel.apply_comparison_highlighting(set(self.last_folder_comparison_results['left_only']), set(results['identical_files']), set(results['diff_files']))
        self.right_panel.apply_comparison_highlighting(set(self.last_folder_comparison_results['right_only']), set(results['identical_files']), set(results['diff_files']))
        
        left_total = self.left_panel.table_widget.rowCount()
        right_total = self.right_panel.table_widget.rowCount()
        
        self.left_panel.status_label.setText(f"Total: {left_total} | Identical: {len(results['identical_files'])}, Different: {len(results['diff_files'])}, Unique: {len(self.last_folder_comparison_results['left_only'])}")
        self.right_panel.status_label.setText(f"Total: {right_total} | Identical: {len(results['identical_files'])}, Different: {len(results['diff_files'])}, Unique: {len(self.last_folder_comparison_results['right_only'])}")
        
        self.generate_folder_comparison_report_text(results) # Generate report text
        print(f"CMD_LOG: Results - Identical: {len(results['identical_files'])}, Different: {len(results['diff_files'])}, Left Only: {len(self.last_folder_comparison_results['left_only'])}, Right Only: {len(self.last_folder_comparison_results['right_only'])}")
        self.update_button_states()
        if self.comparison_thread: self.comparison_thread.quit()

    def compare_text_files(self,):
        if self.left_panel.is_busy or self.right_panel.is_busy: return
        self.left_panel.loading_overlay.start_animation("Comparing..."); self.right_panel.loading_overlay.start_animation("Comparing...")
        QApplication.processEvents()
        file1, file2 = self.left_panel.file_path, self.right_panel.file_path
        try:
            with open(file1, 'r', errors='ignore') as f1, open(file2, 'r', errors='ignore') as f2:
                diff = difflib.unified_diff(f1.readlines(), f2.readlines(), fromfile=os.path.basename(file1), tofile=os.path.basename(file2))
                report = "".join(diff)
                self.show_report_window("Text Comparison Report", report if report else "The selected files are identical.")
        except Exception as e: self.on_comparison_error(f"Text comparison failed: {e}")
        self.left_panel.loading_overlay.stop_animation(); self.right_panel.loading_overlay.stop_animation()

    def on_comparison_error(self, err_msg):
        sys.stdout.write('\n') # Newline in case of error during progress
        sys.stdout.flush()
        print(f"\nCMD_LOG: ERROR during comparison: {err_msg}")
        self.left_panel.loading_overlay.stop_animation(); self.right_panel.loading_overlay.stop_animation()
        if err_msg: QMessageBox.critical(self, "Error", err_msg)
        self.update_button_states()
        
    def move_selected(self, direction):
        if direction == 'left_to_right': source_panel, dest_panel = self.left_panel, self.right_panel
        else: source_panel, dest_panel = self.right_panel, self.left_panel
        files_to_move = source_panel.get_selected_filenames()
        if not files_to_move: return
        print(f"CMD_LOG: Moving {len(files_to_move)} selected files from {source_panel.side_name} to {dest_panel.side_name}.")
        for filename in files_to_move:
            try: shutil.move(os.path.join(source_panel.folder_path, filename), os.path.join(dest_panel.folder_path, filename))
            except Exception as e: QMessageBox.critical(self, "Error", f"Could not move {filename}:\n{e}"); break
        source_panel.load_content(); dest_panel.load_content()
        self.on_panel_cleared()
        
    def move_missing(self, direction):
        if not self.last_folder_comparison_results: return
        if direction == 'left_to_right':
            source_panel, dest_panel, files_to_move = self.left_panel, self.right_panel, self.last_folder_comparison_results['left_only']
        else:
            source_panel, dest_panel, files_to_move = self.right_panel, self.left_panel, self.last_folder_comparison_results['right_only']
        if not files_to_move:
            print(f"CMD_LOG: No unique files to move from {source_panel.side_name}.")
            QMessageBox.information(self, "No Files to Move", "There are no unique files to move from the source."); return
        print(f"CMD_LOG: Moving {len(files_to_move)} missing files from {source_panel.side_name} to {dest_panel.side_name}.")
        for filename in files_to_move:
            try: shutil.copy2(os.path.join(source_panel.folder_path, filename), os.path.join(dest_panel.folder_path, filename))
            except Exception as e: QMessageBox.critical(self, "Error", f"Could not copy {filename}:\n{e}"); break
        dest_panel.load_content(); self.compare_folders()

    def show_report_window(self, title, report_text):
        self.report_win = ReportWindow(title, report_text)
        self.report_win.show()

    def generate_folder_comparison_report_text(self, results):
        report_lines = []
        report_lines.append(f"Folder Comparison Report - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        report_lines.append("=" * 50)
        report_lines.append(f"Left Folder: {self.left_panel.folder_path}")
        report_lines.append(f"Right Folder: {self.right_panel.folder_path}\n")

        report_lines.append("--- Summary ---")
        report_lines.append(f"Total files in Left: {self.left_panel.table_widget.rowCount()}")
        report_lines.append(f"Total files in Right: {self.right_panel.table_widget.rowCount()}")
        report_lines.append(f"Identical files (same content in both): {len(results['identical_files'])}")
        report_lines.append(f"Different files (same name, different content): {len(results['diff_files'])}")
        report_lines.append(f"Files only in Left: {len(self.last_folder_comparison_results['left_only'])}")
        report_lines.append(f"Files only in Right: {len(self.last_folder_comparison_results['right_only'])}\n")

        report_lines.append("--- Files Only in Left ---")
        report_lines.extend(sorted(self.last_folder_comparison_results['left_only']))
        report_lines.append("\n--- Files Only in Right ---")
        report_lines.extend(sorted(self.last_folder_comparison_results['right_only']))
        report_lines.append("\n--- Files in Both (Different Content) ---")
        report_lines.extend(sorted(results['diff_files']))
        report_lines.append("\n--- Files in Both (Identical Content) ---")
        report_lines.extend(sorted(results['identical_files']))

        self.current_folder_report_text = "\n".join(report_lines)

    def show_folder_comparison_report(self):
        if hasattr(self, 'current_folder_report_text') and self.current_folder_report_text:
            self.show_report_window("Folder Comparison Report", self.current_folder_report_text)
        else:
            QMessageBox.information(self, "No Report", "Please run a folder comparison first to generate a report.")

def setup_dark_theme(app):
    app.setStyle("Fusion"); palette = QPalette()
    palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53)); palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.Base, QColor(25, 25, 25)); palette.setColor(QPalette.ColorRole.AlternateBase, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.ToolTipBase, Qt.GlobalColor.black); palette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.white); palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.white); palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.red)
    palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218)); palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor(25, 25, 25)); app.setPalette(palette)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    setup_dark_theme(app)
    window = ModernFileManager()
    window.show()
    sys.exit(app.exec())
