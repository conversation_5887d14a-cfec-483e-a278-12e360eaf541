﻿#pragma checksum "..\..\..\ScrollCaptureOverlayWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FEA56AE50FA4917FDBAEAE89C441EA6CA61587FE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CaptureMasterPro {
    
    
    /// <summary>
    /// ScrollCaptureOverlayWindow
    /// </summary>
    public partial class ScrollCaptureOverlayWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 124 "..\..\..\ScrollCaptureOverlayWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SelectionBorder;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\ScrollCaptureOverlayWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusPanel;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\ScrollCaptureOverlayWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtStatus;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\ScrollCaptureOverlayWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtCaptureCount;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\ScrollCaptureOverlayWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnScrollUp;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\ScrollCaptureOverlayWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnScrollDown;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\ScrollCaptureOverlayWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSave;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\ScrollCaptureOverlayWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.17.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CaptureMasterPro;component/scrollcaptureoverlaywindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ScrollCaptureOverlayWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.17.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 10 "..\..\..\ScrollCaptureOverlayWindow.xaml"
            ((CaptureMasterPro.ScrollCaptureOverlayWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.ScrollCaptureOverlayWindow_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SelectionBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.StatusPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.txtStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.txtCaptureCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.btnScrollUp = ((System.Windows.Controls.Button)(target));
            
            #line 177 "..\..\..\ScrollCaptureOverlayWindow.xaml"
            this.btnScrollUp.Click += new System.Windows.RoutedEventHandler(this.btnScrollUp_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btnScrollDown = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\..\ScrollCaptureOverlayWindow.xaml"
            this.btnScrollDown.Click += new System.Windows.RoutedEventHandler(this.btnScrollDown_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.btnSave = ((System.Windows.Controls.Button)(target));
            
            #line 202 "..\..\..\ScrollCaptureOverlayWindow.xaml"
            this.btnSave.Click += new System.Windows.RoutedEventHandler(this.btnSave_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 212 "..\..\..\ScrollCaptureOverlayWindow.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.btnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

