import { notFound } from 'next/navigation'
import { ProductGrid } from '@/components/products/ProductGrid'
import { CategoryHero } from '@/components/category/CategoryHero'
import { categories, type CategorySlug } from '@/data/categories'
import { getProductsByCategory } from '@/data/products'




interface CategoryPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: CategoryPageProps) {
  const { slug } = await params
  const category = categories[slug as CategorySlug]

  if (!category) {
    return {
      title: 'Categoria non trovata - MetalShop'
    }
  }

  return {
    title: `${category.name} - MetalShop`,
    description: category.description,
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { slug } = await params
  const category = categories[slug as CategorySlug]
  const categoryProducts = getProductsByCategory(slug)

  if (!category) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-black">
      <CategoryHero category={category} />
      <ProductGrid products={categoryProducts} />
    </div>
  )
}