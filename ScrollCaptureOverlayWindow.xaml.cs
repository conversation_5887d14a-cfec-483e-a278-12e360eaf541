using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using CaptureMasterPro.Services;
using System.Runtime.InteropServices;
using System.Windows.Media;
using System.Threading.Tasks;

namespace CaptureMasterPro
{
    public partial class ScrollCaptureOverlayWindow : Window
    {
        public IntPtr TargetWindowHandle { get; set; }
        private ScreenCaptureService _captureService;
        private AutoScrollService _scrollService;
        private ImageEditingService _imageService;
        private List<BitmapSource> _capturedParts = new List<BitmapSource>();
        private MainWindow _mainWindow; // Reference to the MainWindow

        // P/Invoke declarations for window manipulation
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool SetForegroundWindow(IntPtr hWnd);

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;        // x position of upper-left corner
            public int Top;         // y position of upper-left corner
            public int Right;       // x position of lower-right corner
            public int Bottom;      // y position of lower-right corner
        }

        public ScrollCaptureOverlayWindow(IntPtr targetHwnd, MainWindow mainWindow)
        {
            Console.WriteLine("ScrollCaptureOverlayWindow: Constructor - Entered.");
            InitializeComponent();
            TargetWindowHandle = targetHwnd;
            _mainWindow = mainWindow; // Store the reference
            _captureService = new ScreenCaptureService();
            _scrollService = new AutoScrollService();
            _imageService = new ImageEditingService();
            Console.WriteLine("ScrollCaptureOverlayWindow: Constructor - Exited.");
        }

        private async void ScrollCaptureOverlayWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                Console.WriteLine("ScrollCaptureOverlayWindow_Loaded: Entered.");

                _mainWindow.UpdateStatus("Preparing for capture. Checking target window state...");

                if (TargetWindowHandle != IntPtr.Zero && _scrollService.IsWindowMinimized(TargetWindowHandle))
                {
                    _mainWindow.UpdateStatus("Target window is minimized. Attempting to restore...");
                    _scrollService.RestoreWindow(TargetWindowHandle);
                    
                    // Wait for the window to restore and redraw.
                    // Adjust delay if necessary, 500ms is a starting point.
                    await Task.Delay(500);

                    if (_scrollService.IsWindowMinimized(TargetWindowHandle))
                    {
                        _mainWindow.UpdateStatus("Failed to restore target window. Aborting scroll capture.");
                        MessageBox.Show(this,
                                        "The selected window is minimized and could not be restored. Please ensure the window is visible and not minimized before starting scroll capture.",
                                        "Capture Error",
                                        MessageBoxButton.OK,
                                        MessageBoxImage.Error);
                        this.Close();
                        return;
                    }
                    _mainWindow.UpdateStatus("Target window restored.");
                }

                try
                {
                    _mainWindow.UpdateStatus("Positioning overlay...");
                    PositionOverlay();
                    Console.WriteLine("ScrollCaptureOverlayWindow_Loaded: PositionOverlay completed.");
                    _mainWindow.UpdateStatus("Overlay positioned.");
                }
                catch (Exception exPosition)
                {
                    Console.WriteLine($"Error during PositionOverlay: {exPosition.ToString()}");
                    _mainWindow.UpdateStatus($"Error positioning overlay: {exPosition.Message.Substring(0, Math.Min(exPosition.Message.Length, 100))}");
                    MessageBox.Show($"Failed to position scroll capture overlay: {exPosition.Message}", "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    this.Close();
                    return;
                }

                if (TargetWindowHandle != IntPtr.Zero)
                {
                    _mainWindow.UpdateStatus("Preparing for initial capture. Focusing target window...");
                    Console.WriteLine("ScrollCaptureOverlayWindow_Loaded: Attempting to set foreground to target window for initial capture.");
                    bool focused = SetForegroundWindow(TargetWindowHandle); 

                    if (focused) {
                        // Give the OS a moment to process the window switch
                        await Task.Delay(150); 
                        if (AutoScrollService.GetForegroundWindow() == TargetWindowHandle) {
                            Console.WriteLine("ScrollCaptureOverlayWindow_Loaded: Target window confirmed in foreground.");
                        } else {
                            focused = false; // It didn't actually stick
                            Console.WriteLine("ScrollCaptureOverlayWindow_Loaded: SetForegroundWindow succeeded but target is not foreground after delay.");
                        }
                    } else {
                        Console.WriteLine("ScrollCaptureOverlayWindow_Loaded: SetForegroundWindow call failed.");
                    }

                    if (!focused)
                    {
                        _mainWindow.UpdateStatus("Failed to focus target window. Proceeding with caution.");
                    } else {
                        _mainWindow.UpdateStatus("Target window focused for initial capture.");
                    }
                }
                await Task.Delay(50); // Brief pause before UI change
                Console.WriteLine("ScrollCaptureOverlayWindow_Loaded: Hiding ButtonPanel for initial capture.");
                ButtonPanel.Visibility = Visibility.Collapsed;
                await Task.Delay(50); // Allow time for panel to hide

                _mainWindow.UpdateStatus("Performing initial capture...");
                await CaptureCurrentView(); // Initial capture
                _mainWindow.UpdateStatus("Initial capture complete.");
                
                Console.WriteLine("ScrollCaptureOverlayWindow_Loaded: Showing ButtonPanel after initial capture.");
                ButtonPanel.Visibility = Visibility.Visible;
                Console.WriteLine("ScrollCaptureOverlayWindow_Loaded: Finished successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CRITICAL Error in ScrollCaptureOverlayWindow_Loaded: {ex.ToString()}");
                _mainWindow.UpdateStatus($"Error initializing overlay: {ex.Message.Substring(0, Math.Min(ex.Message.Length, 100))}");
                MessageBox.Show($"Failed to initialize scroll capture: {ex.Message}", "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
                this.Close();
            }
        }

        private void PositionOverlay()
        {
            if (TargetWindowHandle == IntPtr.Zero) return;
            Console.WriteLine("PositionOverlay: Entered.");

            RECT targetRect;
            if (GetWindowRect(TargetWindowHandle, out targetRect))
            {
                Console.WriteLine($"PositionOverlay: Target window rect: L={targetRect.Left}, T={targetRect.Top}, R={targetRect.Right}, B={targetRect.Bottom}");
                // Convert screen coordinates to DPI-independent units
                var source = PresentationSource.FromVisual(this);
                double dpiX = 1.0, dpiY = 1.0;
                if (source != null && source.CompositionTarget != null)
                {
                    dpiX = source.CompositionTarget.TransformToDevice.M11;
                    dpiY = source.CompositionTarget.TransformToDevice.M22;
                    Console.WriteLine($"PositionOverlay: DPI X={dpiX}, Y={dpiY}");
                }
                else
                {
                    Console.WriteLine("PositionOverlay: PresentationSource or CompositionTarget is null. Using default DPI of 1.0.");
                }

                if (dpiX == 0) { Console.WriteLine("PositionOverlay: Warning! dpiX is 0. Using 1.0 to avoid division by zero."); dpiX = 1.0; }
                if (dpiY == 0) { Console.WriteLine("PositionOverlay: Warning! dpiY is 0. Using 1.0 to avoid division by zero."); dpiY = 1.0; }

                double newLeft = targetRect.Left / dpiX;
                double newTop = targetRect.Top / dpiY;
                double newWidth = (targetRect.Right - targetRect.Left) / dpiX;
                double newHeight = (targetRect.Bottom - targetRect.Top) / dpiY;
                
                Console.WriteLine($"PositionOverlay: Calculated overlay bounds: L={newLeft}, T={newTop}, W={newWidth}, H={newHeight}");

                this.Left = newLeft;
                this.Top = newTop;
                this.Width = newWidth;
                this.Height = newHeight;
                Console.WriteLine("PositionOverlay: Overlay position and size set.");
            } else {
                Console.WriteLine("PositionOverlay: GetWindowRect failed for target window.");
            }
        }

        private async void btnScrollUp_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Console.WriteLine("ScrollUp button clicked");

                // First, capture the current view before scrolling
                await CaptureCurrentView();

                // Then attempt to scroll up
                bool scrolled = _scrollService.PageUp(TargetWindowHandle);
                Console.WriteLine($"Scroll up result: {scrolled}");

                if (!scrolled)
                {
                    MessageBox.Show("Failed to scroll up. May be at the top of the page or window doesn't support scrolling.",
                                  "Scroll Failed", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                else
                {
                    // Wait a moment for the scroll to complete
                    await Task.Delay(300);
                    Console.WriteLine($"Successfully scrolled up. Total captures: {_capturedImages.Count}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in btnScrollUp_Click: {ex.Message}");
                MessageBox.Show($"An error occurred during the scroll up operation: {ex.Message}", "Scroll Error", MessageBoxButton.OK, MessageBoxImage.Error);
                this.Close(); // Ensure overlay closes to allow MainWindow to reappear
            }
        }

        private async void btnScrollDown_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Console.WriteLine("ScrollDown button clicked");

                // First, capture the current view before scrolling
                await CaptureCurrentView();

                // Then attempt to scroll down
                bool scrolled = _scrollService.PageDown(TargetWindowHandle);
                Console.WriteLine($"Scroll down result: {scrolled}");

                if (!scrolled)
                {
                    // If scrolling failed, check if we can still scroll
                    bool canScroll = _scrollService.CanScroll(TargetWindowHandle);
                    Console.WriteLine($"Can scroll check: {canScroll}");

                    if (!canScroll)
                    {
                        MessageBox.Show("Reached the end of the page. Click 'Save' to compose the final screenshot.",
                                      "Scroll Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("Failed to scroll. The window may not support scrolling or may not be in focus.",
                                      "Scroll Failed", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    // Wait a moment for the scroll to complete
                    await Task.Delay(300);
                    Console.WriteLine($"Successfully scrolled. Total captures: {_capturedImages.Count}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in btnScrollDown_Click: {ex.Message}");
                MessageBox.Show($"An error occurred during the scroll down operation: {ex.Message}", "Scroll Error", MessageBoxButton.OK, MessageBoxImage.Error);
                this.Close(); // Ensure overlay closes to allow MainWindow to reappear
            }
        }

        private async Task PerformScrollAndCapture(Func<bool> scrollAction)
        {
            if (TargetWindowHandle == IntPtr.Zero) return;
            bool continueScrolling = true;
        
            _mainWindow.UpdateStatus("Attempting to focus target window...");
            bool windowFocused = false;
            // More patient focus loop: try for up to ~1.5 seconds
            for (int focusAttempt = 0; focusAttempt < 5 && !windowFocused; focusAttempt++)
            {
                SetForegroundWindow(TargetWindowHandle);
                // Wait progressively longer
                await Task.Delay(100 + (focusAttempt * 100)); // e.g., 100, 200, 300, 400, 500 ms
                if (AutoScrollService.GetForegroundWindow() == TargetWindowHandle)
                {
                    windowFocused = true;
                    _mainWindow.UpdateStatus("Target window focused.");
                    Console.WriteLine($"Target window focused successfully on attempt {focusAttempt + 1}.");
                    break;
                }
                Console.WriteLine($"Focus attempt {focusAttempt + 1} for target window failed.");
            }
        
            if (!windowFocused)
            {
                _mainWindow.UpdateStatus("Failed to focus target window. Scroll may fail.");
                Console.WriteLine("Failed to reliably focus the target window. Proceeding, but scrolling might be unreliable.");
            }
            
            // Increased delay to allow target window to fully settle after focus attempts
            await Task.Delay(300);

            // Initial check if the window is scrollable at all before starting the loop.
            // The first image is already captured by ScrollCaptureOverlayWindow_Loaded.
            // So, if it's not scrollable from the start, the user can just save that first image.
            if (!_scrollService.CanScroll(TargetWindowHandle))
            {
                _mainWindow.UpdateStatus("Target window does not appear to be scrollable.");
                Console.WriteLine("PerformScrollAndCapture: Target window not scrollable from the start (according to CanScroll).");
                ShowScrollFailedNotification("Window content does not appear to be scrollable.");
                ButtonPanel.Visibility = Visibility.Visible; // Ensure buttons are visible if we exit early
                return; // Exit if not scrollable
            }
        
            ButtonPanel.Visibility = Visibility.Collapsed;
            await Task.Delay(50); // Allow UI to update
        
            // Loop as long as we can scroll
            int scrollCount = 0;
            while(continueScrolling) {
                scrollCount++;
                _mainWindow.UpdateStatus($"Attempting scroll #{scrollCount}...");

                // Check if scrolling is possible before attempting the scroll action
                if (!_scrollService.CanScroll(TargetWindowHandle))
                {
                    _mainWindow.UpdateStatus("Reached end of scrollable content (CanScroll reported false).");
                    Console.WriteLine($"PerformScrollAndCapture: CanScroll returned false at scroll attempt {scrollCount}. Assuming end of content.");
                    ShowScrollFailedNotification("Reached the end of the scrollable content.");
                    continueScrolling = false;
                    break; // Exit the loop
                }
                
                BitmapSource? lastImage = _capturedParts.Count > 0 ? _capturedParts[_capturedParts.Count - 1] : null;

                // Ensure target window is focused before attempting to scroll
                // Even though scrollAction() internals might do this, an extra check here can be beneficial.
                if (AutoScrollService.GetForegroundWindow() != TargetWindowHandle)
                {
                    Console.WriteLine($"PerformScrollAndCapture: Target window lost focus before scroll attempt {scrollCount}. Refocusing.");
                    SetForegroundWindow(TargetWindowHandle);
                    await Task.Delay(150); // Give a moment for focus to switch
                }
        
                // Attempt the scroll action.
                // The CanScroll() check before this iteration already confirmed scrolling *should* be possible.
                bool scrollAttemptedSuccessfully = scrollAction();

                if (!scrollAttemptedSuccessfully)
                {
                    _mainWindow.UpdateStatus($"Scroll action failed at step {scrollCount}. Checking content change as a fallback.");
                    Console.WriteLine($"PerformScrollAndCapture: scrollAction() returned false at scroll attempt {scrollCount}.");
                    // Even if scrollAction returns false, we might have scrolled a tiny bit,
                    // or it might be a false negative. The image comparison is the ultimate test.
                    // However, if CanScroll was true, and scrollAction is false, and images are same, then definitely stop.
                }

                _mainWindow.UpdateStatus($"Scroll #{scrollCount} attempted. Waiting for render...");
                // This delay is critical for the scrolled content to be redrawn before we capture.
                await Task.Delay(1200); // Increased delay for content rendering

                await CaptureCurrentView(); // Captures and adds the new view to _capturedParts
                BitmapSource? newImage = _capturedParts.Count > 0 ? _capturedParts[_capturedParts.Count - 1] : null;
        
                if (lastImage != null && newImage != null && _imageService.AreImagesIdentical(lastImage, newImage))
                {
                    _mainWindow.UpdateStatus("Scroll did not produce new content or failed. Stopping.");
                    Console.WriteLine("PerformScrollAndCapture: Image comparison detected no change after scroll attempt. Assuming end of scrollable content or failed scroll.");

                    _capturedParts.RemoveAt(_capturedParts.Count - 1); // Remove the duplicate image that was just added

                    ShowScrollFailedNotification("No new content found after scrolling.");
                    continueScrolling = false; // This is the crucial break condition
                }
                else if (newImage == null) // Should not happen if CaptureCurrentView works
                {
                    _mainWindow.UpdateStatus("Failed to capture new image. Stopping.");
                    Console.WriteLine("PerformScrollAndCapture: newImage is null after CaptureCurrentView. Critical error.");
                    ShowScrollFailedNotification("Error capturing image after scroll.");
                    continueScrolling = false;
                }
                else
                {
                     _mainWindow.UpdateStatus($"View captured after scroll #{scrollCount}.");
                }
        
                if (scrollCount >= 25) // Safety break to prevent infinite loops
                {
                    _mainWindow.UpdateStatus("Max scroll attempts reached. Stopping.");
                    Console.WriteLine("PerformScrollAndCapture: Max scroll attempts (25) reached. Stopping.");
                    ShowScrollFailedNotification("Maximum scroll attempts reached.");
                    continueScrolling = false;
                    // No break here, let the loop condition handle termination at the start of the next iteration
                }
            }
            
            ButtonPanel.Visibility = Visibility.Visible;
        }
        
        private void ShowScrollFailedNotification(string message = "Unable to scroll further. Page may have reached the end.")
        {
            var notification = new Border
            {
                Background = new SolidColorBrush(Color.FromArgb(200, 255, 255, 0)), // Semi-transparent yellow
                BorderBrush = Brushes.Orange,
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(10),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Bottom, // Changed to bottom to avoid button overlap
                Margin = new Thickness(0, 0, 0, 80), // Position above the bottom edge
                IsHitTestVisible = false // Make it non-interactive so it doesn't block clicks
            };
            
            var text = new TextBlock
            {
                Text = "Unable to scroll further. Page may have reached the end.",
                Foreground = Brushes.Black,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center
            };
            
            notification.Child = text;
            
            // Add to overlay grid
            if (this.Content is Border border && border.Child is Grid grid)
            {
                // Set ZIndex to ensure it appears above other elements but doesn't block interaction
                Panel.SetZIndex(notification, 1000);
                grid.Children.Add(notification);
                
                // Auto-remove after 3 seconds
                var timer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(3)
                };
                timer.Tick += (s, e) =>
                {
                    grid.Children.Remove(notification);
                    timer.Stop();
                };
                timer.Start();
            }
        }

        private async Task CaptureCurrentView()
        {
            if (TargetWindowHandle == IntPtr.Zero) return;

            SetForegroundWindow(TargetWindowHandle); // Ensure target window is active before capture
            // Allow time for window to become active and content to render
            await Task.Delay(150);

            try
            {
                BitmapSource? capturedImage = _captureService.CaptureWindow(TargetWindowHandle);
                if (capturedImage != null)
                {
                    _capturedParts.Add(capturedImage);
                    Console.WriteLine($"Captured part {_capturedParts.Count}, Size: {capturedImage.PixelWidth}x{capturedImage.PixelHeight}");
                }
                else
                {
                    Console.WriteLine("CaptureCurrentView: Failed to capture image - capturedImage is null");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error capturing window part: {ex.Message}", "Capture Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Console.WriteLine($"Error in CaptureCurrentView: {ex.Message}");
            }
        }

        private async void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Capture the final view if we haven't captured anything yet or if we need the last frame
                if (_capturedParts.Count == 0)
                {
                    Console.WriteLine("No captures yet, capturing current view before saving");
                    await CaptureCurrentView();
                }
                else
                {
                    // Capture the final view to ensure we have the complete page
                    Console.WriteLine("Capturing final view before saving");
                    await CaptureCurrentView();
                }

                if (_capturedParts.Count == 0)
                {
                    MessageBox.Show("No images were captured.", "Save Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                Console.WriteLine($"Stitching {_capturedParts.Count} captured parts");

                // The ImageEditingService should have a method to stitch images
                ImageEditingService imageService = new ImageEditingService();
                BitmapSource? finalImage = null; // Initialize to null

                if (_capturedParts.Count == 1)
                {
                    // If only one part, use it directly
                    finalImage = _capturedParts[0];
                    Console.WriteLine("Single image captured, using directly");
                }
                else if (_capturedParts.Count > 1)
                {
                    // Stitch multiple parts together
                    finalImage = imageService.StitchImages(_capturedParts, StitchDirection.Vertical);
                    Console.WriteLine($"Stitched {_capturedParts.Count} images into final image");
                }

                if (finalImage == null) {
                    MessageBox.Show("Failed to create a final stitched image. No parts were captured or stitching failed.", "Save Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    _mainWindow.UpdateStatus("Scrolling capture failed to produce an image.");
                    this.Close();
                    return;
                }

                // Pass the final image back to MainWindow to display and save
                _mainWindow.SetCurrentImage(finalImage);
                _mainWindow.ShowImageDisplay();
                _mainWindow.UpdateImageInfo();
                _mainWindow.UpdateStatus($"Scrolling capture complete. Composed {_capturedParts.Count} screenshots.");

                MessageBox.Show($"Scrolling capture complete! Composed {_capturedParts.Count} screenshots into a single long image. The image is ready to be saved from the main window.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in btnSave_Click: {ex.Message}");
                MessageBox.Show($"Error saving stitched image: {ex.Message}", "Save Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                this.Close();
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            _capturedParts.Clear(); // Discard any captured parts
            _mainWindow.UpdateStatus("Scrolling capture cancelled.");
            this.Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            Console.WriteLine("ScrollCaptureOverlayWindow.OnClosed called.");
            base.OnClosed(e);
            // MainWindow's own handler for the overlay's Closed event
            // is now responsible for showing and activating the MainWindow.
            // Any logic here to activate _mainWindow is likely redundant.
            try
            {
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during ScrollCaptureOverlayWindow.OnClosed: {ex.ToString()}");
            }
        }
    }
}
