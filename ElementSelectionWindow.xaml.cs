using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace CaptureMasterPro
{
    public enum SelectionType
    {
        ContentArea,    // Green - Content only (browser page, document content)
        ClientArea,     // Blue - Application client area
        FullWindow      // Red - Full window including chrome
    }

    public class SelectionResult
    {
        public Rectangle Bounds { get; set; }
        public SelectionType Type { get; set; }
        public IntPtr WindowHandle { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public partial class ElementSelectionWindow : Window
    {
        // P/Invoke declarations for window detection
        [DllImport("user32.dll")]
        static extern IntPtr WindowFromPoint(POINT pt);

        [DllImport("user32.dll")]
        [return: Marshal<PERSON>(UnmanagedType.Bool)]
        static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool GetClientRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool ClientToScreen(IntPtr hWnd, ref POINT lpPoint);

        [DllImport("user32.dll")]
        static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll")]
        static extern int GetClassName(IntPtr hWnd, System.Text.StringBuilder lpClassName, int nMaxCount);

        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int X;
            public int Y;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        private DispatcherTimer _mouseTimer = null!;
        private SelectionResult? _currentSelection;
        private IntPtr _lastDetectedWindow = IntPtr.Zero;
        private IntPtr _targetWindowHandle = IntPtr.Zero;

        public SelectionResult? SelectedArea { get; private set; }

        public ElementSelectionWindow()
        {
            InitializeComponent();
            InitializeMouseTracking();
        }

        public ElementSelectionWindow(IntPtr targetWindowHandle)
        {
            InitializeComponent();
            _targetWindowHandle = targetWindowHandle;
            InitializeMouseTracking();
        }

        private void InitializeMouseTracking()
        {
            _mouseTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(50) // 20 FPS for smooth tracking
            };
            _mouseTimer.Tick += MouseTimer_Tick;
            _mouseTimer.Start();

            // Handle mouse clicks and keyboard input
            this.MouseLeftButtonDown += ElementSelectionWindow_MouseLeftButtonDown;
            this.KeyDown += ElementSelectionWindow_KeyDown;
            this.Loaded += ElementSelectionWindow_Loaded;
        }

        private void ElementSelectionWindow_Loaded(object sender, RoutedEventArgs e)
        {
            this.Focus();
        }

        private void MouseTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                // Get current mouse position in screen coordinates
                var mousePos = System.Windows.Forms.Cursor.Position;
                POINT point = new POINT { X = mousePos.X, Y = mousePos.Y };

                Console.WriteLine($"Mouse position: {point.X}, {point.Y}");

                IntPtr hwnd;

                // If we have a target window, focus only on that window and its children
                if (_targetWindowHandle != IntPtr.Zero)
                {
                    // Check if mouse is within the target window bounds
                    if (IsPointInWindow(_targetWindowHandle, point))
                    {
                        // Get the specific child window under mouse within target window
                        hwnd = WindowFromPoint(point);

                        // Ensure the detected window is the target window or its child
                        if (!IsWindowOrChild(_targetWindowHandle, hwnd))
                        {
                            hwnd = _targetWindowHandle; // Fall back to target window
                        }
                    }
                    else
                    {
                        // Mouse is outside target window, don't highlight anything
                        ClearHighlight();
                        return;
                    }
                }
                else
                {
                    // Original behavior - detect any window under mouse
                    hwnd = WindowFromPoint(point);
                }

                Console.WriteLine($"Window handle: {hwnd}");

                if (hwnd == IntPtr.Zero)
                {
                    Console.WriteLine("No window found under mouse");
                    return;
                }

                // Skip our own window handle
                IntPtr ourHandle = this.GetHandle();
                if (hwnd == ourHandle)
                {
                    Console.WriteLine("Skipping our own window");
                    return;
                }

                // Skip if same window as last detection to avoid flicker
                if (hwnd == _lastDetectedWindow)
                    return;

                _lastDetectedWindow = hwnd;
                DetectAndHighlightElement(hwnd, point);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in mouse tracking: {ex.Message}");
            }
        }

        private bool IsPointInWindow(IntPtr hwnd, POINT point)
        {
            RECT windowRect;
            if (GetWindowRect(hwnd, out windowRect))
            {
                return point.X >= windowRect.Left && point.X <= windowRect.Right &&
                       point.Y >= windowRect.Top && point.Y <= windowRect.Bottom;
            }
            return false;
        }

        private bool IsWindowOrChild(IntPtr parentHwnd, IntPtr childHwnd)
        {
            if (parentHwnd == childHwnd)
                return true;

            IntPtr current = childHwnd;
            while (current != IntPtr.Zero)
            {
                current = GetParent(current);
                if (current == parentHwnd)
                    return true;
            }
            return false;
        }

        [DllImport("user32.dll")]
        static extern IntPtr GetParent(IntPtr hWnd);

        private void ClearHighlight()
        {
            // Clear any existing highlight
            selectionFrame.Visibility = Visibility.Collapsed;
            _currentSelection = null;
        }

        private void DetectAndHighlightElement(IntPtr hwnd, POINT mousePoint)
        {
            try
            {
                // Get window information
                var windowTitle = GetWindowTitle(hwnd);
                var className = GetWindowClassName(hwnd);
                
                // Determine the best selection type based on window type and mouse position
                var selectionType = DetermineSelectionType(hwnd, className, mousePoint);
                var bounds = GetSelectionBounds(hwnd, selectionType);
                
                if (bounds.Width > 0 && bounds.Height > 0)
                {
                    _currentSelection = new SelectionResult
                    {
                        Bounds = bounds,
                        Type = selectionType,
                        WindowHandle = hwnd,
                        Description = GetSelectionDescription(selectionType, windowTitle, className)
                    };

                    UpdateSelectionFrame(bounds, selectionType);
                    UpdateSelectionText(_currentSelection.Description);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error detecting element: {ex.Message}");
            }
        }

        private SelectionType DetermineSelectionType(IntPtr hwnd, string className, POINT mousePoint)
        {
            // Get window and client rectangles
            GetWindowRect(hwnd, out RECT windowRect);
            GetClientRect(hwnd, out RECT clientRect);
            
            // Convert client rect to screen coordinates
            POINT clientTopLeft = new POINT { X = 0, Y = 0 };
            ClientToScreen(hwnd, ref clientTopLeft);
            
            Rectangle windowBounds = new Rectangle(windowRect.Left, windowRect.Top, 
                windowRect.Right - windowRect.Left, windowRect.Bottom - windowRect.Top);
            Rectangle clientBounds = new Rectangle(clientTopLeft.X, clientTopLeft.Y,
                clientRect.Right - clientRect.Left, clientRect.Bottom - clientRect.Top);

            // Calculate mouse position relative to window
            int relativeX = mousePoint.X - windowRect.Left;
            int relativeY = mousePoint.Y - windowRect.Top;
            
            // Determine selection based on mouse position and window type
            bool isInClientArea = mousePoint.X >= clientBounds.X && mousePoint.X < clientBounds.Right &&
                                 mousePoint.Y >= clientBounds.Y && mousePoint.Y < clientBounds.Bottom;

            // For browsers and document viewers, prefer content area when mouse is in center
            if (IsBrowserOrDocumentWindow(className))
            {
                if (isInClientArea)
                {
                    // If mouse is in the central area, suggest content-only capture
                    int centerThreshold = Math.Min(clientBounds.Width, clientBounds.Height) / 4;
                    bool isInCenterArea = relativeX > centerThreshold && 
                                         relativeX < (clientBounds.Width - centerThreshold) &&
                                         relativeY > centerThreshold && 
                                         relativeY < (clientBounds.Height - centerThreshold);
                    
                    return isInCenterArea ? SelectionType.ContentArea : SelectionType.ClientArea;
                }
            }

            // Default logic based on position
            if (isInClientArea)
                return SelectionType.ClientArea;
            else
                return SelectionType.FullWindow;
        }

        private bool IsBrowserOrDocumentWindow(string className)
        {
            string[] browserClasses = { "Chrome_WidgetWin", "MozillaWindowClass", "ApplicationFrameWindow", 
                                       "EdgeWebView", "XLMAIN", "OpusApp", "AcrobatSDIWindow" };
            
            foreach (string browserClass in browserClasses)
            {
                if (className.Contains(browserClass, StringComparison.OrdinalIgnoreCase))
                    return true;
            }
            return false;
        }

        private Rectangle GetSelectionBounds(IntPtr hwnd, SelectionType selectionType)
        {
            switch (selectionType)
            {
                case SelectionType.FullWindow:
                    GetWindowRect(hwnd, out RECT windowRect);
                    return new Rectangle(windowRect.Left, windowRect.Top,
                        windowRect.Right - windowRect.Left, windowRect.Bottom - windowRect.Top);

                case SelectionType.ClientArea:
                    return GetClientAreaBounds(hwnd);

                case SelectionType.ContentArea:
                    return GetContentAreaBounds(hwnd);

                default:
                    return Rectangle.Empty;
            }
        }

        private Rectangle GetClientAreaBounds(IntPtr hwnd)
        {
            if (GetClientRect(hwnd, out RECT clientRect))
            {
                POINT topLeft = new POINT { X = 0, Y = 0 };
                if (ClientToScreen(hwnd, ref topLeft))
                {
                    return new Rectangle(topLeft.X, topLeft.Y,
                        clientRect.Right - clientRect.Left, clientRect.Bottom - clientRect.Top);
                }
            }
            return Rectangle.Empty;
        }

        private Rectangle GetContentAreaBounds(IntPtr hwnd)
        {
            // For content area, we estimate the scrollable content region
            // This is a simplified version - could be enhanced with more sophisticated detection
            var clientBounds = GetClientAreaBounds(hwnd);
            
            if (clientBounds.IsEmpty)
                return clientBounds;

            // Estimate content area by removing typical UI elements (simplified)
            int topOffset = 60;    // Typical toolbar/address bar height
            int bottomOffset = 25; // Status bar height
            int sideOffset = 10;   // Scrollbar width
            
            return new Rectangle(
                clientBounds.X + sideOffset,
                clientBounds.Y + topOffset,
                Math.Max(100, clientBounds.Width - (sideOffset * 2)),
                Math.Max(100, clientBounds.Height - topOffset - bottomOffset)
            );
        }

        private string GetWindowTitle(IntPtr hwnd)
        {
            var title = new System.Text.StringBuilder(256);
            GetWindowText(hwnd, title, title.Capacity);
            return title.ToString();
        }

        private string GetWindowClassName(IntPtr hwnd)
        {
            var className = new System.Text.StringBuilder(256);
            GetClassName(hwnd, className, className.Capacity);
            return className.ToString();
        }

        private string GetSelectionDescription(SelectionType type, string windowTitle, string className)
        {
            string typeDesc = type switch
            {
                SelectionType.ContentArea => "🟢 Content Area Only",
                SelectionType.ClientArea => "🔵 Application Content",
                SelectionType.FullWindow => "🔴 Full Window",
                _ => "Unknown"
            };

            string windowDesc = !string.IsNullOrEmpty(windowTitle) ? windowTitle : className;
            if (windowDesc.Length > 40)
                windowDesc = windowDesc.Substring(0, 37) + "...";

            return $"{typeDesc} - {windowDesc}";
        }

        private void UpdateSelectionFrame(Rectangle bounds, SelectionType type)
        {
            // Convert screen coordinates to WPF coordinates
            var source = PresentationSource.FromVisual(this);
            double dpiX = 1.0, dpiY = 1.0;
            if (source?.CompositionTarget != null)
            {
                dpiX = source.CompositionTarget.TransformToDevice.M11;
                dpiY = source.CompositionTarget.TransformToDevice.M22;
            }

            double left = bounds.X / dpiX;
            double top = bounds.Y / dpiY;
            double width = bounds.Width / dpiX;
            double height = bounds.Height / dpiY;

            // Set frame color based on selection type
            string color = type switch
            {
                SelectionType.ContentArea => "#FF00FF00", // Green
                SelectionType.ClientArea => "#FF0080FF",  // Blue
                SelectionType.FullWindow => "#FFFF0000",  // Red
                _ => "#FFFFFF00" // Yellow fallback
            };

            selectionFrame.BorderBrush = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(color));
            
            selectionFrame.Margin = new Thickness(left, top, 0, 0);
            selectionFrame.Width = width;
            selectionFrame.Height = height;
            selectionFrame.Visibility = Visibility.Visible;
        }

        private void UpdateSelectionText(string description)
        {
            txtCurrentSelection.Text = description;
        }

        private void ElementSelectionWindow_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_currentSelection != null)
            {
                SelectedArea = _currentSelection;
                this.DialogResult = true;
                this.Close();
            }
        }

        private void ElementSelectionWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _mouseTimer?.Stop();
            base.OnClosed(e);
        }

        private IntPtr GetHandle()
        {
            return new System.Windows.Interop.WindowInteropHelper(this).Handle;
        }
    }
}
