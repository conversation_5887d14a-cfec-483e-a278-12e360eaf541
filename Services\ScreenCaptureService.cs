using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Media.Imaging;
using System.Windows.Media; // Added for DPI scaling

namespace CaptureMasterPro.Services
{
    public class ScreenCaptureService
    {
        public BitmapSource CaptureScreen(IntPtr hwnd = default)
        {
            Rectangle bounds;
            double dpiScaleX = 1.0;
            double dpiScaleY = 1.0;

            // Get DPI scaling factor if a MainWindow is available
            if (Application.Current != null && Application.Current.MainWindow != null)
            {
                PresentationSource source = PresentationSource.FromVisual(Application.Current.MainWindow);
                if (source != null && source.CompositionTarget != null)
                {
                    Matrix m = source.CompositionTarget.TransformToDevice;
                    dpiScaleX = m.M11;
                    dpiScaleY = m.M22;
                }
            }
            
            if (hwnd == default)
            {
                // Capture entire screen - use full virtual screen dimensions to capture all monitors
                // SystemParameters are in DIPs, convert to physical pixels
                bounds = new Rectangle(
                    (int)(SystemParameters.VirtualScreenLeft * dpiScaleX),
                    (int)(SystemParameters.VirtualScreenTop * dpiScaleY),
                    (int)(SystemParameters.VirtualScreenWidth * dpiScaleX),
                    (int)(SystemParameters.VirtualScreenHeight * dpiScaleY));
                
                Console.WriteLine($"Capturing entire screen (scaled): Left={bounds.Left}, Top={bounds.Top}, Width={bounds.Width}, Height={bounds.Height}");
                Console.WriteLine($"SystemParameters (DIPs): VScreenLeft={SystemParameters.VirtualScreenLeft}, VScreenTop={SystemParameters.VirtualScreenTop}, VScreenWidth={SystemParameters.VirtualScreenWidth}, VScreenHeight={SystemParameters.VirtualScreenHeight}");
                Console.WriteLine($"DPI Scale: X={dpiScaleX}, Y={dpiScaleY}");
                
                // Verify the bounds are valid for full screen capture
                if (bounds.Width <= 0 || bounds.Height <= 0)
                {
                    Console.WriteLine("Scaled virtual screen dimensions are invalid. Falling back to primary screen.");
                    // Fallback to primary screen if virtual screen parameters are invalid, also scale these
                    bounds = new Rectangle(0, 0, 
                        (int)(SystemParameters.PrimaryScreenWidth * dpiScaleX), 
                        (int)(SystemParameters.PrimaryScreenHeight * dpiScaleY));
                    Console.WriteLine($"Using fallback scaled primary screen dimensions: Width={bounds.Width}, Height={bounds.Height}");
                }
                
                // Ensure we're capturing the entire screen, not just a portion
                Console.WriteLine("Ensuring full screen capture with correct (scaled) dimensions");
            }
            else
            {
                // Capture specific window
                GetWindowRect(hwnd, out RECT rect);
                bounds = new Rectangle(rect.Left, rect.Top, rect.Right - rect.Left, rect.Bottom - rect.Top);
                Console.WriteLine($"Capturing window: Left={bounds.Left}, Top={bounds.Top}, Width={bounds.Width}, Height={bounds.Height}");
            }
            
            // Add a small delay to ensure everything is ready for capture
            System.Threading.Thread.Sleep(100);

            using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb))
            {
                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    // Set high quality settings for better capture
                    g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                    g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                    g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                    
                    // Verify bitmap dimensions match the bounds
                    Console.WriteLine($"Preparing to capture: Bounds={bounds.Width}x{bounds.Height}, Bitmap={bitmap.Width}x{bitmap.Height}");
                    if (bitmap.Width != bounds.Width || bitmap.Height != bounds.Height)
                    {
                        Console.WriteLine($"WARNING: Bitmap size does not match bounds! Bitmap={bitmap.Width}x{bitmap.Height}, Bounds={bounds.Width}x{bounds.Height}");
                    }
                    
                    try
                    {
                        // Ensure we capture the entire screen by using the correct parameters
                        // Make sure we're capturing from the correct starting position and using the full bounds
                        g.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size, CopyPixelOperation.SourceCopy);
                        Console.WriteLine("Screen capture completed successfully");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error during screen capture: {ex.Message}");
                        // If there's an error, try with a different approach
                        if (hwnd == default) // Only for full screen capture
                        {
                            Console.WriteLine("Attempting alternative capture method...");
                            // Try with primary screen dimensions as fallback
                            bounds = new Rectangle(0, 0, 
                                (int)SystemParameters.PrimaryScreenWidth, 
                                (int)SystemParameters.PrimaryScreenHeight);
                            g.CopyFromScreen(0, 0, 0, 0, bounds.Size, CopyPixelOperation.SourceCopy);
                        }
                    }
                    
                    // Log the captured dimensions
                    Console.WriteLine($"Bitmap created with dimensions: {bitmap.Width}x{bitmap.Height}");
                }
                
                return ConvertBitmapToBitmapSource(bitmap);
            }
        }
        
        public BitmapSource CaptureArea(Rectangle bounds)
        {
            // Ensure the bounds are valid
            if (bounds.Width <= 0 || bounds.Height <= 0)
            {
                throw new ArgumentException($"Invalid capture area dimensions: {bounds.Width}x{bounds.Height}");
            }
            
            Console.WriteLine($"CaptureArea: Capturing area with bounds: X={bounds.X}, Y={bounds.Y}, Width={bounds.Width}, Height={bounds.Height}");
            
            using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb))
            {
                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    // Set high quality settings for better capture
                    g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                    g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                    g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                    
                    // Copy the exact area from screen with SourceCopy operation for better quality
                    g.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size, CopyPixelOperation.SourceCopy);
                    
                    // Log the captured dimensions
                    Console.WriteLine($"Area bitmap created with dimensions: {bitmap.Width}x{bitmap.Height}");
                }
                
                return ConvertBitmapToBitmapSource(bitmap);
            }
        }

        public BitmapSource CaptureActiveWindow()
        {
            IntPtr hwnd = GetForegroundWindow();
            return CaptureScreen(hwnd);
        }
        
        public BitmapSource CaptureWindow(IntPtr hwnd)
        {
            if (hwnd == IntPtr.Zero)
            {
                throw new ArgumentException("Handle della finestra non valido");
            }

            // Get window rectangle
            GetWindowRect(hwnd, out RECT rect);
            Rectangle bounds = new Rectangle(rect.Left, rect.Top, rect.Right - rect.Left, rect.Bottom - rect.Top);

            Console.WriteLine($"CaptureWindow: Window bounds: Left={bounds.Left}, Top={bounds.Top}, Width={bounds.Width}, Height={bounds.Height}");

            // For window capture, we don't apply DPI scaling as the window coordinates are already in physical pixels
            using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb))
            {
                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    // Set high quality settings for better capture
                    g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                    g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                    g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;

                    try
                    {
                        // Capture the window content directly without DPI scaling
                        g.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size, CopyPixelOperation.SourceCopy);
                        Console.WriteLine($"Window captured successfully: {bitmap.Width}x{bitmap.Height}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error capturing window: {ex.Message}");
                        throw;
                    }
                }

                return ConvertBitmapToBitmapSource(bitmap);
            }
        }

        private BitmapSource ConvertBitmapToBitmapSource(Bitmap bitmap)
        {
            var bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, bitmap.Width, bitmap.Height),
                ImageLockMode.ReadOnly, bitmap.PixelFormat);

            // Make sure we're using the correct pixel format based on the bitmap's format
            System.Windows.Media.PixelFormat pixelFormat;
            
            // Choose the appropriate pixel format based on the bitmap's format
            switch (bitmap.PixelFormat)
            {
                case System.Drawing.Imaging.PixelFormat.Format32bppArgb:
                    pixelFormat = System.Windows.Media.PixelFormats.Bgra32;
                    break;
                case System.Drawing.Imaging.PixelFormat.Format24bppRgb:
                    pixelFormat = System.Windows.Media.PixelFormats.Bgr24;
                    break;
                default:
                    pixelFormat = System.Windows.Media.PixelFormats.Bgra32;
                    break;
            }
            
            var bitmapSource = BitmapSource.Create(
                bitmapData.Width, bitmapData.Height,
                bitmap.HorizontalResolution, bitmap.VerticalResolution,
                pixelFormat, null,
                bitmapData.Scan0, bitmapData.Stride * bitmapData.Height, bitmapData.Stride);

            bitmap.UnlockBits(bitmapData);
            return bitmapSource;
        }

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }
    }
}