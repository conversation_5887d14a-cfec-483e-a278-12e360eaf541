import { useStream } from "@langchain/langgraph-sdk/react";
import type { Message } from "@langchain/langgraph-sdk";
import { useState, useEffect, useRef, useCallback } from "react";
import { ProcessedEvent } from "@/components/ActivityTimeline";
import { WelcomeScreen } from "@/components/WelcomeScreen";
import { ChatMessagesView } from "@/components/ChatMessagesView";
import { HistoryPanel } from "@/components/HistoryPanel";

const API_URL = import.meta.env.DEV
  ? "http://localhost:2024"
  : "http://localhost:8123";

export default function App() {
  const [processedEventsTimeline, setProcessedEventsTimeline] = useState<
    ProcessedEvent[]
  >([]);
  const [historicalActivities, setHistoricalActivities] = useState<
    Record<string, ProcessedEvent[]>
  >({});
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const hasFinalizeEventOccurredRef = useRef(false);
  const [history, setHistory] = useState<any[]>([]);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const thread = useStream<{
    messages: Message[];
    initial_search_query_count: number;
    max_research_loops: number;
    reasoning_model: string;
  }>({
    apiUrl: API_URL,
    assistantId: "agent",
    threadId,
    onThreadId: setThreadId,
    messagesKey: "messages",
    onUpdateEvent: (event: any) => {
      let processedEvent: ProcessedEvent | null = null;
      if (event.generate_query) {
        processedEvent = {
          title: "Generating Search Queries",
          data: event.generate_query?.search_query?.join(", ") || "",
        };
      } else if (event.web_research) {
        const sources = event.web_research.sources_gathered || [];
        const numSources = sources.length;
        const uniqueLabels = [
          ...new Set(sources.map((s: any) => s.label).filter(Boolean)),
        ];
        const exampleLabels = uniqueLabels.slice(0, 3).join(", ");
        processedEvent = {
          title: "Web Research",
          data: `Gathered ${numSources} sources. Related to: ${
            exampleLabels || "N/A"
          }.`,
        };
      } else if (event.reflection) {
        processedEvent = {
          title: "Reflection",
          data: "Analysing Web Research Results",
        };
      } else if (event.finalize_answer) {
        processedEvent = {
          title: "Finalizing Answer",
          data: "Composing and presenting the final answer.",
        };
        hasFinalizeEventOccurredRef.current = true;
      }
      if (processedEvent) {
        setProcessedEventsTimeline((prevEvents) => [
          ...prevEvents,
          processedEvent!,
        ]);
      }
    },
    onFinish: async (threadState) => {
      if (threadId) {
        const lastMessage =
          threadState.values.messages[
            threadState.values.messages.length - 1
          ];
        let updatedHistoricalActivities = { ...historicalActivities };
        if (lastMessage && lastMessage.type === "ai" && lastMessage.id) {
          updatedHistoricalActivities = {
            ...historicalActivities,
            [lastMessage.id]: [...processedEventsTimeline],
          };
          setHistoricalActivities(updatedHistoricalActivities);
        }

        const newHistoryItem = {
          session_id: threadId,
          messages: threadState.values.messages,
          historicalActivities: updatedHistoricalActivities,
        };

        try {
          await fetch(`${API_URL}/sessions`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(newHistoryItem),
          });
          setHistory((prevHistory) => [newHistoryItem, ...prevHistory]);
        } catch (error) {
          console.error("Failed to save history:", error);
        }
      }
    },
    onError: (error: any) => {
      console.error(error);
    },
  });

  useEffect(() => {
    async function fetchHistory() {
      try {
        const response = await fetch(`${API_URL}/sessions`);
        const data = await response.json();
        setHistory(data);
      } catch (error) {
        console.error("Failed to fetch history:", error);
      }
    }
    fetchHistory();
  }, []);

  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollViewport = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (scrollViewport) {
        scrollViewport.scrollTop = scrollViewport.scrollHeight;
      }
    }
  }, [messages]);

  useEffect(() => {
    if (thread.messages.length > messages.length) {
      setMessages(thread.messages);
    }
  }, [thread.messages, messages]);

  const handleSelectHistory = (historyItem: any) => {
    setThreadId(historyItem.session_id);
    setMessages(historyItem.messages);
    setHistoricalActivities(historyItem.historicalActivities || {});
    setProcessedEventsTimeline([]);
  };

  const handleSubmit = useCallback(
    async (submittedInputValue: string, effort: string, model: string) => {
      if (!submittedInputValue.trim() || thread.isLoading) return;
      setProcessedEventsTimeline([]);
      hasFinalizeEventOccurredRef.current = false;
      if (!threadId) {
        setThreadId(new Date().toISOString());
      }

      // convert effort to, initial_search_query_count and max_research_loops
      // low means max 1 loop and 1 query
      // medium means max 3 loops and 3 queries
      // high means max 10 loops and 5 queries
      let initial_search_query_count = 0;
      let max_research_loops = 0;
      switch (effort) {
        case "low":
          initial_search_query_count = 1;
          max_research_loops = 1;
          break;
        case "medium":
          initial_search_query_count = 3;
          max_research_loops = 3;
          break;
        case "high":
          initial_search_query_count = 5;
          max_research_loops = 10;
          break;
      }

      const newMessages: Message[] = [
        ...messages,
        {
          type: "human",
          content: submittedInputValue,
          id: Date.now().toString(),
        },
      ];
      setMessages(newMessages);
      thread.submit({
        messages: newMessages,
        initial_search_query_count: initial_search_query_count,
        max_research_loops: max_research_loops,
        reasoning_model: model,
      });
    },
    [thread, threadId, messages]
  );

  const handleCancel = useCallback(() => {
    thread.stop();
  }, [thread]);

  return (
    <div className="flex h-screen bg-neutral-800 text-neutral-100 font-sans antialiased">
      <HistoryPanel
        history={history}
        onSelectHistory={handleSelectHistory}
      />
      <main className="h-full w-full max-w-4xl mx-auto">
        {messages.length === 0 ? (
          <WelcomeScreen
            handleSubmit={handleSubmit}
            isLoading={thread.isLoading}
            onCancel={handleCancel}
          />
        ) : (
          <ChatMessagesView
            messages={messages}
            isLoading={thread.isLoading}
            scrollAreaRef={scrollAreaRef}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            liveActivityEvents={processedEventsTimeline}
            historicalActivities={historicalActivities}
          />
        )}
      </main>
    </div>
  );
}